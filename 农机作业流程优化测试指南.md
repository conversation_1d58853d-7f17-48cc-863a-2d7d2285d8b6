# 农机作业流程优化测试指南

## 概述

本次优化对农机作业相关的功能进行了全面升级，删除了所有模拟数据，准备与后端进行联调。主要改进包括：

1. **删除所有模拟数据**：清理job-progress页面的模拟数据和相关代码
2. **增强作业创建功能**：添加作业地点输入和高德地图定位
3. **完善作业流程**：添加开始作业按钮，完整的作业状态管理
4. **实时数据更新**：每30秒获取打捆数量和经纬度数据

## 后端接口文档

### 1. 创建作业接口
- **请求地址**：`POST /job/create`
- **请求头**：`Authorization: Bearer {token}`
- **请求参数**：
  ```json
  {
    "machineId": "农机ID",
    "userId": "用户ID", 
    "username": "用户名",
    "jobName": "作业名称",
    "orderNjname": "作业农机名称",
    "orderName": "作业名称", 
    "orderWhere": "作业地点",
    "orderJw": "经度,纬度",
    "orderSj": "作业司机",
    "orderTime": "2024-01-30T10:30:00.000Z",
    "orderMemo": "作业备注"
  }
  ```
- **响应格式**：
  ```json
  {
    "code": 200,
    "message": "作业创建成功",
    "data": {
      "jobId": "作业ID",
      "status": "created"
    }
  }
  ```

### 2. 开始作业接口
- **请求地址**：`POST /job/start`
- **请求头**：`Authorization: Bearer {token}`
- **请求参数**：
  ```json
  {
    "jobId": "作业ID",
    "userId": "用户ID",
    "username": "用户名",
    "action": "start"
  }
  ```
- **响应格式**：
  ```json
  {
    "code": 200,
    "message": "作业启动成功",
    "data": {
      "jobId": "作业ID",
      "status": "running",
      "startTime": "2024-01-30T10:30:00.000Z"
    }
  }
  ```

### 3. 实时数据获取接口
- **请求地址**：`GET /job/realtime`
- **请求头**：`Authorization: Bearer {token}`
- **请求参数**：
  ```json
  {
    "jobId": "作业ID",
    "userId": "用户ID",
    "username": "用户名"
  }
  ```
- **响应格式**：
  ```json
  {
    "code": 200,
    "message": "获取成功",
    "data": {
      "orderKs": 120,
      "orderJw": "113.08684478,28.20519649"
    }
  }
  ```

### 4. 暂停/停止作业接口
- **暂停**：`POST /job/pause`
- **停止**：`POST /job/stop`
- **请求参数**：
  ```json
  {
    "jobId": "作业ID",
    "userId": "用户ID",
    "username": "用户名"
  }
  ```

## 功能测试流程

### 阶段1：作业创建测试

#### 1.1 进入农机详情页面
1. 从农机列表选择一台农机
2. 点击进入详情页面
3. 点击"新增作业"按钮

#### 1.2 填写作业信息
1. **作业名称测试**：
   - 留空：应自动生成名称
   - 填写：使用自定义名称
   - 长度限制：最大50字符

2. **作业地点测试**：
   - **手动输入**：直接输入地点名称
   - **自动定位**：点击定位按钮
     - 成功：显示当前位置，自动填入地点
     - 失败：显示错误提示和解决方案
   - **必填验证**：不填写应提示错误

3. **定位功能测试**：
   ```javascript
   // 期望的定位数据格式
   {
     longitude: 113.08684478,
     latitude: 28.20519649,
     address: "湖南省长沙市某农场" // 可选
   }
   ```

#### 1.3 提交创建
1. **表单验证**：
   - 地点为空时提示"请输入作业地点"
   - 其他字段可选

2. **成功创建**：
   - 显示"作业创建成功"提示
   - 跳转到job-progress页面
   - 全局数据正确传递

3. **失败处理**：
   - 401：跳转到登录页面
   - 其他错误：显示具体错误信息

### 阶段2：作业进度页面测试

#### 2.1 页面初始化
1. **状态检查**：
   - 初始状态应为"待开始"
   - 状态指示器显示⏳图标
   - 状态徽章显示橙色"待开始"

2. **作业信息显示**：
   - **作业农机**：显示农机名称
   - **作业名称**：显示作业或自动生成的名称
   - **作业日期**：显示YYYY-MM-DD格式
   - **作业司机**：显示司机姓名
   - **作业地点**：显示输入的地点
   - **备注信息**：显示创建信息

3. **按钮状态**：
   - 只显示"开始作业"按钮
   - 暂停和停止按钮隐藏

#### 2.2 开始作业测试
1. **点击开始作业**：
   - 按钮显示加载状态
   - 发送start指令给后端
   - 成功后状态变为"进行中"

2. **状态更新**：
   - 图标变为🚜
   - 状态文字变为"进行中"
   - 开始计时器和定时刷新

3. **按钮更新**：
   - 开始按钮隐藏
   - 显示"暂停作业"和"停止作业"按钮

#### 2.3 实时计时测试
1. **时间格式**：
   - 显示"X分XX秒"格式
   - 每秒自动更新
   - 从开始时间计算准确

2. **暂停恢复**：
   - 暂停时停止计时
   - 恢复时继续计时
   - 停止时显示最终时长

#### 2.4 实时数据更新测试
1. **自动刷新**：
   - 每30秒自动请求后端数据
   - 只在"运行中"状态刷新
   - 显示刷新状态指示器

2. **数据更新**：
   - **打捆数量**：从`orderKs`字段获取
   - **经纬度**：从`orderJw`字段获取，格式"经度,纬度"
   - **位置显示**：解析经纬度并显示

3. **错误处理**：
   - 网络错误：在控制台记录
   - 认证失败：跳转登录
   - 数据格式错误：兜底处理

#### 2.5 作业控制测试
1. **暂停作业**：
   - 点击暂停按钮
   - 状态变为"已暂停"
   - 停止数据刷新和计时

2. **继续作业**：
   - 点击继续按钮
   - 状态变为"进行中"
   - 恢复数据刷新和计时

3. **停止作业**：
   - 点击停止按钮
   - 显示确认对话框
   - 确认后调用停止API
   - 显示作业完成结果

## 调试信息说明

### 创建作业调试信息
```javascript
📋 创建作业，提交数据: {
  jobName: "...",
  workLocation: "...", 
  coordinates: "113.08684478,28.20519649",
  machineInfo: {...}
}

🚜 创建作业 API 请求参数: {
  token: "***有token***",
  username: "...",
  userId: "...",
  machineId: "...",
  jobData: {...}
}

🚜 创建作业 API 响应: {...}
```

### 开始作业调试信息
```javascript
🚀 开始作业，发送start指令给后端

🚀 开始作业 API 请求参数: {
  token: "***有token***",
  username: "...",
  userId: "...",
  jobId: "...",
  action: "start"
}

🚀 开始作业 API 响应: {...}
```

### 实时数据调试信息
```javascript
📊 正在获取实时数据...

📊 获取实时作业数据 API 请求参数: {
  token: "***有token***",
  username: "...",
  userId: "...",
  jobId: "..."
}

📊 实时数据获取成功: {
  orderKs: 120,
  orderJw: "113.08684478,28.20519649"
}
```

### 定位功能调试信息
```javascript
📍 开始获取当前位置...
📍 位置获取成功: {
  longitude: 113.08684478,
  latitude: 28.20519649,
  address: "湖南省长沙市某农场"
}
```

## 数据流向图

```
农机详情页面
    ↓ (点击新增作业)
填写作业信息
    ↓ (创建作业)
后端创建作业记录
    ↓ (跳转)
作业进度页面(待开始状态)
    ↓ (点击开始作业)
后端接收start指令
    ↓ (开始成功)
作业状态变为进行中
    ↓ (每30秒)
获取实时数据(orderKs, orderJw)
    ↓ (更新显示)
前端展示实时信息
```

## 测试检查清单

### ✅ 创建作业功能
- [ ] 地点输入框正常显示
- [ ] 定位按钮功能正常
- [ ] 定位成功后自动填入地点
- [ ] 表单验证正确
- [ ] 创建成功跳转正常
- [ ] 全局数据传递正确

### ✅ 作业进度页面
- [ ] 初始状态为"待开始"
- [ ] 作业信息正确显示
- [ ] 开始按钮功能正常
- [ ] 实时计时精确
- [ ] 状态切换正确
- [ ] 按钮显示逻辑正确

### ✅ 实时数据功能
- [ ] 30秒自动刷新
- [ ] 打捆数量正确更新
- [ ] 经纬度解析正确
- [ ] 网络错误处理
- [ ] 状态指示器正常

### ✅ 接口调用
- [ ] 所有API包含username参数
- [ ] 错误处理完整
- [ ] 认证失败正确处理
- [ ] 调试信息详细

### ✅ UI/UX体验
- [ ] 界面风格一致
- [ ] 加载状态清晰
- [ ] 错误提示友好
- [ ] 操作反馈及时

## 常见问题排查

### 1. 定位功能不工作
**可能原因**：
- 权限未授予
- 网络问题
- 设备不支持

**解决方案**：
- 检查位置权限设置
- 确认网络连接
- 查看控制台错误信息

### 2. 作业状态不更新
**可能原因**：
- API调用失败
- 数据格式不匹配
- token过期

**解决方案**：
- 检查网络连接
- 查看API响应数据
- 确认用户登录状态

### 3. 实时数据不刷新
**可能原因**：
- 定时器未启动
- API接口问题
- 作业状态不正确

**解决方案**：
- 确认作业状态为"running"
- 检查API接口返回
- 查看定时器日志

### 4. 页面数据传递失败
**可能原因**：
- 全局数据未设置
- 页面参数缺失
- 数据结构不匹配

**解决方案**：
- 检查globalData设置
- 确认页面跳转参数
- 验证数据结构

## 后续开发建议

1. **地图集成**：后续可以集成高德地图显示实时位置
2. **历史轨迹**：记录作业轨迹供后续分析
3. **异常监控**：添加设备异常状态监控
4. **数据统计**：提供作业效率统计分析
5. **离线模式**：支持网络断开时的离线作业

通过以上全面的测试，可以确保农机作业流程的稳定性和用户体验，为后续的生产使用打下坚实基础。
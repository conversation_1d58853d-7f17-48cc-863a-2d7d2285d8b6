<template>
	<view class="container">
		<!-- 顶部导航 -->
		<view class="header">
			<view class="back-btn" @click="goBack">
				<text class="back-icon">‹</text>
			</view>
			<text class="header-title">订单详情</text>
			<view class="header-placeholder"></view>
		</view>

		<!-- 订单基本信息区域 -->
		<view class="order-info-section">
			<view class="section-title">
				<text class="title-icon">📋</text>
				<text class="title-text">订单基本信息</text>
			</view>
			
			<view class="info-card">
				<view class="info-grid">
					<view class="info-item">
						<text class="info-label">作业农机名称</text>
						<text class="info-value">{{ orderDetail.orderNjname || '未知农机' }}</text>
					</view>
					<view class="info-item">
						<text class="info-label">作业名称</text>
						<text class="info-value">{{ orderDetail.orderName || '未命名作业' }}</text>
					</view>
					<view class="info-item">
						<text class="info-label">作业面积</text>
						<text class="info-value">{{ orderDetail.orderMj || '0' }}亩</text>
					</view>
					<view class="info-item">
						<text class="info-label">作业时间</text>
						<text class="info-value">{{ formatDate(orderDetail.orderTime) }}</text>
					</view>
					<view class="info-item">
						<text class="info-label">作业司机</text>
						<text class="info-value">{{ orderDetail.orderSj || '未知司机' }}</text>
					</view>
					<view class="info-item">
						<text class="info-label">作业数量</text>
						<text class="info-value">{{ orderDetail.orderKs || '0' }}</text>
					</view>
					<view class="info-item">
						<text class="info-label">作业地点</text>
						<text class="info-value">{{ orderDetail.orderWhere || '未知地点' }}</text>
					</view>
					<view class="info-item full-width" v-if="orderDetail.orderMemo">
						<text class="info-label">作业备注</text>
						<text class="info-value memo">{{ orderDetail.orderMemo }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 农机运动轨迹区域 -->
		<view class="track-section">
			<view class="section-title">
				<text class="title-icon">🗺️</text>
				<text class="title-text">农机运动轨迹</text>
				<view class="track-actions">
					<view class="refresh-track-btn" @click="refreshTrackData" :class="{ 'loading': trackLoading }">
						<text class="refresh-icon">🔄</text>
						<text class="refresh-text">{{ trackLoading ? '加载中...' : '刷新轨迹' }}</text>
					</view>
				</view>
			</view>
			
			<!-- 地图容器 -->
			<view class="map-container">
				<view class="map-wrapper" v-if="mapImageUrl">
					<image 
						class="map-image" 
						:src="mapImageUrl" 
						mode="aspectFit"
						@load="onMapLoad"
						@error="onMapError"
					/>
					<view class="map-overlay" v-if="trackPoints.length > 0">
						<text class="track-info">轨迹点数: {{ trackPoints.length }}</text>
					</view>
				</view>
				
				<!-- 地图加载状态 -->
				<view class="map-loading" v-else-if="trackLoading">
					<text class="loading-icon">⏳</text>
					<text class="loading-text">正在生成轨迹地图...</text>
				</view>
				
				<!-- 无轨迹数据状态 -->
				<view class="no-track" v-else>
					<text class="no-track-icon">📍</text>
					<text class="no-track-title">暂无轨迹数据</text>
					<text class="no-track-desc">该订单暂时没有农机运动轨迹数据</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { getUserInfo } from '@/common/utils/auth.js';
import { getOrderTrackData } from '@/common/api/machines.js';
import { TrackUtils } from '@/common/utils/mapHelper.js';

export default {
	data() {
		return {
			orderId: '',
			userInfo: null,
			orderDetail: {},
			trackPoints: [],
			mapImageUrl: '',
			trackLoading: false
		}
	},
	onLoad(options) {
		this.orderId = options.orderId || '';
		this.userInfo = getUserInfo();
		this.loadOrderDetail();
		this.loadTrackData();
	},
	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack();
		},
		
		// 加载订单详情
		loadOrderDetail() {
			// 从全局数据获取订单详情
			const globalData = getApp().globalData;
			if (globalData && globalData.currentOrderDetail) {
				this.orderDetail = globalData.currentOrderDetail;
				console.log('📋 订单详情:', this.orderDetail);
			} else {
				console.warn('⚠️ 未找到订单详情数据');
				uni.showToast({
					title: '订单数据加载失败',
					icon: 'none'
				});
			}
		},
		
		// 加载轨迹数据
		async loadTrackData() {
			if (!this.userInfo || !this.orderId) {
				console.warn('⚠️ 缺少必要参数，无法加载轨迹数据');
				return;
			}
			
			this.trackLoading = true;
			
			try {
				console.log('🗺️ 开始加载订单轨迹数据...');
				const result = await getOrderTrackData(this.userInfo, this.orderId);
				
				if (result.code === 200 && result.data && result.data.length > 0) {
					this.trackPoints = result.data;
					console.log('🗺️ 轨迹数据加载成功，共', this.trackPoints.length, '个点');
					this.generateTrackMap();
				} else {
					console.log('🗺️ 暂无轨迹数据');
					this.trackPoints = [];
					this.mapImageUrl = '';
				}
			} catch (error) {
				console.error('❌ 加载轨迹数据失败:', error);
				uni.showToast({
					title: '轨迹数据加载失败',
					icon: 'none'
				});
				this.trackPoints = [];
				this.mapImageUrl = '';
			} finally {
				this.trackLoading = false;
			}
		},
		
		// 生成轨迹地图
		generateTrackMap() {
			if (!this.trackPoints || this.trackPoints.length === 0) {
				console.log('🗺️ 无轨迹点，无法生成地图');
				return;
			}

			try {
				console.log('🗺️ 开始生成轨迹地图...');

				// 过滤有效的轨迹点
				const validTrackPoints = TrackUtils.filterValidTrackPoints(this.trackPoints);
				if (validTrackPoints.length === 0) {
					console.warn('⚠️ 没有有效的轨迹点');
					return;
				}

				// 使用轨迹工具生成地图URL
				this.mapImageUrl = TrackUtils.generateTrackMapUrl(validTrackPoints, {
					width: 600,
					height: 400,
					pathStyles: '0xff0000,3,0.8' // 红色轨迹线，宽度3，透明度0.8
				});

				console.log('🗺️ 轨迹地图生成成功:', this.mapImageUrl);
			} catch (error) {
				console.error('❌ 生成轨迹地图失败:', error);
				uni.showToast({
					title: '地图生成失败',
					icon: 'none'
				});
			}
		},
		
		// 刷新轨迹数据
		refreshTrackData() {
			this.loadTrackData();
		},
		
		// 地图加载成功
		onMapLoad() {
			console.log('🗺️ 地图图片加载成功');
		},
		
		// 地图加载失败
		onMapError() {
			console.error('❌ 地图图片加载失败');
			uni.showToast({
				title: '地图加载失败',
				icon: 'none'
			});
		},
		
		// 格式化日期
		formatDate(dateStr) {
			if (!dateStr) return '未知时间';
			
			try {
				const date = new Date(dateStr);
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				const hours = String(date.getHours()).padStart(2, '0');
				const minutes = String(date.getMinutes()).padStart(2, '0');
				
				return `${year}-${month}-${day} ${hours}:${minutes}`;
			} catch (error) {
				return '时间格式错误';
			}
		}
	}
}
</script>

<style>
.container {
	background-color: #f8f8f8;
	min-height: 100vh;
}

/* 顶部导航 */
.header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 30rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #fff;
}

.back-btn {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 50%;
	background-color: rgba(255, 255, 255, 0.2);
}

.back-icon {
	font-size: 36rpx;
	font-weight: bold;
}

.header-title {
	font-size: 32rpx;
	font-weight: bold;
}

.header-placeholder {
	width: 60rpx;
}

/* 区域标题 */
.section-title {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx 30rpx 20rpx;
}

.title-icon {
	font-size: 28rpx;
	margin-right: 10rpx;
}

.title-text {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	flex: 1;
}

/* 订单信息区域 */
.order-info-section {
	margin-bottom: 20rpx;
}

.info-card {
	background-color: #fff;
	margin: 0 20rpx;
	border-radius: 15rpx;
	padding: 30rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.info-grid {
	display: flex;
	flex-wrap: wrap;
}

.info-item {
	width: 50%;
	margin-bottom: 25rpx;
	padding-right: 20rpx;
}

.info-item.full-width {
	width: 100%;
	padding-right: 0;
}

.info-label {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 8rpx;
	display: block;
}

.info-value {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
	display: block;
}

.info-value.memo {
	line-height: 1.4;
	color: #666;
}

/* 轨迹区域 */
.track-section {
	margin-bottom: 20rpx;
}

.track-actions {
	display: flex;
	align-items: center;
}

.refresh-track-btn {
	display: flex;
	align-items: center;
	padding: 10rpx 20rpx;
	background: linear-gradient(135deg, #007AFF 0%, #0056CC 100%);
	color: #fff;
	border-radius: 20rpx;
	font-size: 24rpx;
}

.refresh-track-btn.loading {
	opacity: 0.7;
}

.refresh-icon {
	font-size: 20rpx;
	margin-right: 8rpx;
}

.refresh-track-btn.loading .refresh-icon {
	animation: rotate 1s linear infinite;
}

@keyframes rotate {
	from { transform: rotate(0deg); }
	to { transform: rotate(360deg); }
}

/* 地图容器 */
.map-container {
	margin: 0 20rpx;
	background-color: #fff;
	border-radius: 15rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.map-wrapper {
	position: relative;
}

.map-image {
	width: 100%;
	height: 400rpx;
}

.map-overlay {
	position: absolute;
	top: 20rpx;
	right: 20rpx;
	background-color: rgba(0, 0, 0, 0.7);
	color: #fff;
	padding: 10rpx 15rpx;
	border-radius: 10rpx;
	font-size: 22rpx;
}

/* 地图状态 */
.map-loading,
.no-track {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 80rpx 40rpx;
	text-align: center;
}

.loading-icon,
.no-track-icon {
	font-size: 80rpx;
	margin-bottom: 20rpx;
	opacity: 0.5;
}

.loading-text,
.no-track-title {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 10rpx;
}

.no-track-desc {
	font-size: 24rpx;
	color: #999;
	line-height: 1.4;
}
</style>

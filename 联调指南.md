# 农机列表功能前后端联调指南

## 🚨 登录接口问题排查

### 当前遇到的问题
**错误信息**: `JSON parse error: Cannot deserialize value of type 'java.lang.String' from Object value`

**问题原因**: 后端期望接收字符串格式的数据，但前端发送了对象格式

### ✅ 已修复的问题
1. **API调用方式**: 修复了登录API调用时传递对象而非独立参数的问题
2. **数据格式**: 确保发送的JSON数据格式正确
3. **测试工具**: 添加了专门的登录接口测试功能
4. **跳转问题**: 修复了登录成功后无法跳转的问题

## 🔐 认证失败问题排查

### 新发现的问题
**错误信息**: `"认证失败，无法访问系统资源", "code": 401`

**问题分析**: 这是后端认证问题，可能的原因：
1. **Token格式问题**: 前端发送的token格式不正确
2. **Token过期**: 登录token已过期
3. **认证头缺失**: 请求头中缺少Authorization字段

### ✅ 已修复的认证问题
1. **添加认证头**: 在农机列表API请求中添加了`Authorization: Bearer {token}`
2. **认证失败处理**: 当收到401错误时，自动清除用户信息并跳转到登录页
3. **错误提示**: 提供明确的认证失败提示信息

## 🚨 后端接口冲突问题

### 新发现的问题
**错误信息**: `"Ambiguous handler methods mapped for '/fram/order/getOrderbyname'"`

**问题分析**: 这是**后端问题**，不是前端问题
- 后端有两个方法映射到了同一个URL路径
- `getOrderbyname(String)` 和 `getInfo(Long)` 都映射到了 `/fram/order/getOrderbyname`

### ✅ 已添加的错误处理
1. **错误识别**: 前端能识别并提示这种特定错误
2. **用户提示**: 明确告知用户这是后端问题，需要联系后端开发者
3. **调试信息**: 在调试模式下显示"后端错误"状态

### 🔧 后端需要修复
**建议后端开发者**：
1. 检查 `NjOrderController` 中的方法映射
2. 确保每个URL路径只映射到一个方法
3. 可能需要修改其中一个方法的路径或参数

## 🎯 URL路径参数问题修复

### 🔍 发现的关键问题
**后端接口定义**:
```java
@GetMapping(value = "/{orderSj}")
public AjaxResult getOrderbyname(@PathVariable("orderSj") String orderSj)
```

**问题分析**:
- 后端期望 `orderSj` 作为**路径参数**，不是查询参数
- 前端之前错误地使用了 `/fram/order/getOrderbyname?orderSj=xxx`
- 应该使用 `/fram/order/{orderSj}` 格式

### ✅ 已修复的URL格式
- **错误格式**: `http://192.168.1.105:8080/fram/order/getOrderbyname?orderSj=13327297187`
- **正确格式**: `http://192.168.1.105:8080/fram/order/13327297187?token=xxx`

### 📝 修复内容
1. **API调用修复**: 将 `orderSj` 从查询参数改为路径参数
2. **URL构建**: 动态构建包含用户手机号的URL
3. **参数传递**: 只在查询参数中传递 `token`
4. **测试工具更新**: API测试工具也使用正确的URL格式

## 🎯 性能优化

### ✅ 已优化的数据加载策略
1. **按需加载**: 只有在农机列表页面才会获取详细的农机数据
2. **其他页面优化**: 
   - 地图页面：只获取统计数据，不获取农机列表
   - 管理页面：只获取统计数据
   - 历史页面：直接获取历史记录，不依赖农机列表
3. **减少网络请求**: 避免不必要的API调用，提升应用性能

## 🔄 刷新功能

### ✅ 已添加的刷新机制
1. **下拉刷新**: 在农机列表页面下拉可以刷新数据
2. **上拉刷新**: 滑动到页面底部自动触发刷新
3. **手动刷新**: 页面顶部有"刷新数据"按钮，可以手动触发刷新
4. **智能提示**: 刷新时显示加载状态和完成提示

### 🎮 使用方法
- **下拉刷新**: 在农机列表页面向下拉动页面
- **上拉刷新**: 滑动到页面底部会自动触发
- **手动刷新**: 点击页面顶部的"🔄 刷新数据"按钮

## 📋 联调前准备

### 1. 已确认的后端接口信息 ✅
- **接口地址**: `http://*************:8080`
- **农机列表接口路径**: `/fram/order/getOrderbyname`
- **请求方式**: GET
- **请求参数**: token（用户令牌）、orderSj（用户手机号，实际就是username）
- **返回数据格式**: 订单对象数组

### 2. 当前代码配置
在 `common/api/machines.js` 文件中：
```javascript
const BASE_URL = 'http://*************:8080';
const USE_MOCK_DATA = false; // false=使用真实接口，true=使用模拟数据
```

## 🔧 当前联调步骤

### 第一步：确认接口地址 ✅
后端接口完整地址：`http://*************:8080/fram/order/getOrderbyname`

### 第二步：确认请求参数 ✅
```javascript
// GET请求参数
{
    token: userInfo.token,        // 用户登录token
    orderSj: userInfo.username    // 用户手机号（实际就是username）
}
```

### 第三步：测试接口调用
1. 使用测试账号登录系统
2. 进入农机列表页面，点击"显示调试"
3. 或者使用API测试工具页面进行测试
4. 查看浏览器开发者工具（F12）的Console日志

### 第四步：验证返回数据
后端应该返回类似以下格式的数据：
```javascript
[
    {
        id: 3,
        orderNjname: '收割机003号',
        orderName: '维护保养',
        orderMj: '0',
        orderTime: '2023-05-14',
        orderSj: '王五',
        orderJw: '113.077030,28.192149',
        orderKs: 0,
        orderWhere: '维修站',
        orderMemo: '定期保养，更换滤芯'
    }
    // ... 更多订单数据
]
```

## 🚀 接下来的联调步骤

### 1. 立即测试
1. **登录系统**（使用测试账号：13800138000 / 123456）
2. **进入农机列表页面**
3. **开启调试模式**（点击"显示调试"按钮）
4. **查看接口状态**：
   - 如果显示"成功"，数据来源"服务器" → 联调成功！
   - 如果显示"失败"，数据来源"本地模拟" → 需要排查问题

### 2. 使用API测试工具
1. 访问API测试页面（`pages/api-test/api-test.vue`）
2. 确认配置：
   - 基础地址：`http://*************:8080`
   - 接口路径：`/fram/order/getOrderbyname`
   - 请求方式：`GET`
3. 点击"测试接口"查看详细结果

### 3. 问题排查
如果接口调用失败，请检查：

**A. 网络连接**
- 确认能ping通 `*************`
- 确认后端服务是否启动

**B. 参数传递**
- 检查Console日志中的"获取订单列表 API 请求参数"
- 确认token和orderSj都有值
- 确认orderSj的值就是用户的手机号

**C. 后端响应**
- 查看Console日志中的"获取订单列表 API 响应"
- 确认返回的数据格式

## 📊 数据格式处理

### 前端已支持的返回格式
代码已经处理了多种可能的返回格式：

1. **直接返回数组**：
```javascript
[{订单1}, {订单2}, ...]
```

2. **标准格式**：
```javascript
{
    "code": 200,
    "message": "获取订单列表成功",
    "data": [{订单1}, {订单2}, ...]
}
```

3. **包含data字段**：
```javascript
{
    "data": [{订单1}, {订单2}, ...],
    "message": "成功"
}
```

4. **使用success字段**：
```javascript
{
    "success": true,
    "message": "成功",
    "data": [{订单1}, {订单2}, ...]
}
```

## 🔍 调试技巧

### 1. 查看Console日志
按F12打开开发者工具，查看Console中的日志：
- `获取订单列表 API 请求参数` - 确认发送的参数
- `获取订单列表 API 响应` - 查看后端返回的数据

### 2. 使用调试模式
在农机列表页面点击"显示调试"，查看：
- 接口状态：成功/失败
- 数据来源：服务器/本地模拟
- 最后请求时间
- 数据条数

### 3. 临时切换模拟数据
如果需要继续开发其他功能，可以临时切换：
```javascript
// 在 common/api/machines.js 中
const USE_MOCK_DATA = true; // 切换到模拟数据
```

## 📞 与后端沟通要点

### 需要确认的问题：
1. **接口是否正常启动**？
2. **是否需要特殊的请求头**？
3. **token格式是否正确**？
4. **返回数据的确切格式是什么**？
5. **如果没有数据时返回什么**？
6. **错误情况下返回什么**？

### 常见问题及解决方案：

**问题1：404错误**
- 确认接口路径是否正确
- 确认后端服务是否启动

**问题2：参数错误**
- 确认参数名称是否正确（token、orderSj）
- 确认参数值是否有效
- 确认orderSj传递的是用户手机号

**问题3：数据格式问题**
- 查看实际返回的数据格式
- 根据实际格式调整前端处理逻辑

## ✅ 联调成功标志

- [ ] 接口能正常调用（状态码200）
- [ ] 返回真实的订单数据
- [ ] 农机列表页面能正常显示数据
- [ ] 调试信息显示"成功"和"服务器"
- [ ] 搜索和筛选功能正常工作
- [ ] 确认orderSj参数正确传递用户手机号

## 🎯 下一步计划

联调成功后，可以继续完善：
1. **错误处理优化**
2. **数据刷新机制**
3. **分页功能**（如果需要）
4. **详情页面联调**
5. **其他功能模块联调**

### 测试账号
- 管理员：13800138000 / 123456（拥有最高权限，可查看所有农机数据）

### 页面配置优化
- 为农机列表页面启用下拉刷新功能
- 设置上拉加载距离为50px
- 添加API测试工具页面到pages.json

## 📊 模拟数据说明

### 农机数据概览
系统现已包含丰富的模拟数据，管理员登录后可查看：

**农机设备**：20台收割机（001号-020号）
- 水稻收割机：6台
- 小麦收割机：5台  
- 玉米收割机：4台
- 大豆收割机：4台
- 维护保养中：3台

**作业区域**：覆盖湖南省长沙市岳麓区农田A-P区
- 总作业面积：2,400+ 亩
- 总打包数量：4,800+ 包
- 作业时间：2023年5月15日-18日

**工作历史**：16条详细作业记录
- 包含GPS轨迹数据
- 详细的作业时间记录
- 完整的工作量统计

### 数据特点
1. **真实性**：模拟数据基于实际农机作业场景设计
2. **完整性**：包含农机信息、作业记录、GPS轨迹等完整数据链
3. **多样性**：涵盖不同作物类型、不同作业状态、不同地理位置
4. **权限控制**：管理员可查看所有数据，普通用户仅可查看授权数据

### 使用建议
- 使用管理员账号（13800138000）登录可查看完整数据
- 在农机列表页面可以看到所有20台设备
- 在地图页面可以看到设备分布情况
- 在历史页面可以查看详细作业记录

## 当前状态 
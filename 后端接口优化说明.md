# 后端接口优化说明

## 概述
根据与后端协商的结果，对农机作业相关接口进行了优化调整。

## 修改内容

### 1. 接口地址更新

#### 原接口地址：
- 新增作业：`/fram/order/add`
- 开始作业：`/fram/order` 
- 暂停作业：`/fram/order`
- 停止作业：`/fram/order`
- 实时数据：`/fram/order/realtime`

#### 现接口地址：
- 新增作业：`/fram/order`
- 开始作业：`/fram/order`
- 暂停作业：`/fram/order`
- 停止作业：`/fram/order`
- 实时数据：`/fram/order/realtime`

### 2. 新增作业数据字段优化

#### 原字段名：
```javascript
{
    jobName: "作业名称",
    orderMemo: "由用户创建的描述"
}
```

#### 优化后字段名：
```javascript
{
    orderName: "作业名称",  // 后端要求使用orderName而不是jobName
    orderMemo: "13888888888"  // 用username作为筛查该用户所有作业记录的标识符
}
```

### 3. 实时数据获取优化

#### 原实时数据包含：
- `orderKs`：打捆数量
- `orderJw`：当前经纬度坐标

#### 优化后实时数据：
- `orderKs`：打捆数量（保留）
- ~~`orderJw`：当前经纬度坐标~~（移除）

## 接口详细说明

### 1. 新增作业接口
- **接口地址**：`POST /fram/order`
- **请求参数**：
```javascript
{
    machineId: "农机ID",
    userId: "用户ID", 
    username: "用户名",
    orderName: "作业名称",
    orderWhere: "作业地点",
    orderJw: "作业经纬度",
    orderSj: "作业司机",
    orderNjname: "作业农机名称",
    orderTime: "作业时间",
    orderMemo: "username值" // 用于筛查用户作业记录
}
```

### 2. 作业控制接口
- **接口地址**：`POST /fram/order`
- **请求参数**：
```javascript
{
    jobId: "作业ID",
    userId: "用户ID",
    username: "用户名",
    action: "start|pause|stop" // 操作类型
}
```

### 3. 实时数据获取接口
- **接口地址**：`GET /fram/order/realtime`
- **查询参数**：
```javascript
{
    jobId: "作业ID",
    username: "用户名"
}
```
- **返回数据**：
```javascript
{
    code: 200,
    data: {
        orderKs: 15 // 打捆数量
    }
}
```

## 文件修改列表

### 1. `common/api/machines.js`
- 更新了所有作业相关接口的URL地址
- 修改了开始、暂停、停止作业的action参数

### 2. `pages/machine-detail/machine-detail.vue`
- 将 `jobName` 改为 `orderName`
- 将 `orderMemo` 设置为 `username` 值
- 更新了调试日志

### 3. `pages/job-progress/job-progress.vue`
- 移除了实时数据中的经纬度更新逻辑
- 只保留 `orderKs`（打捆数量）的实时更新
- 优化了调试信息

## 测试要点

1. **新增作业测试**：
   - 确认提交的数据中 `orderName` 字段正确
   - 确认 `orderMemo` 字段包含正确的 `username` 值

2. **实时数据测试**：
   - 确认每30秒只获取 `orderKs` 数据
   - 确认不再更新经纬度信息

3. **作业控制测试**：
   - 确认开始、暂停、停止作业都使用相同的接口地址
   - 确认 `action` 参数正确传递

## 注意事项

1. 所有作业相关接口都需要在请求中包含 `username` 参数
2. `orderMemo` 字段现在专门用于存储用户名，用于后端筛查
3. 实时数据获取现在只关注打捆数量，不再处理坐标信息
4. 所有接口都需要 `Authorization: Bearer {token}` 头部认证

## 更新时间
2025-01-30
<script>
import { checkLoginStatus } from './common/api/user.js';

export default {
	globalData: {
		userInfo: null,
		currentMachine: null // 存储当前查看的农机数据
	},
	onLaunch: function() {
		console.log('App Launch');
		// 初始化检查登录状态
		this.checkAuth();
	},
	onShow: function() {
		console.log('App Show');
	},
	onHide: function() {
		console.log('App Hide');
	},
	methods: {
		// 检查登录状态
		async checkAuth() {
			try {
				const userInfo = await checkLoginStatus();
				this.globalData.userInfo = userInfo;
			} catch (err) {
				console.log('未登录或登录已过期');
				this.globalData.userInfo = null;
			}
		}
	}
}
</script>

<style>
	/*每个页面公共css */
	page {
		font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
		font-size: 28rpx;
		line-height: 1.5;
		background-color: #f8f8f8;
		color: #333;
	}

	/* 通用样式 */
	.container {
		padding: 20rpx;
	}

	.flex-row {
		display: flex;
		flex-direction: row;
	}

	.flex-column {
		display: flex;
		flex-direction: column;
	}

	.flex-center {
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.flex-between {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.card {
		background-color: #fff;
		border-radius: 12rpx;
		padding: 20rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	}

	.btn-primary {
		background-color: #007AFF;
		color: #fff;
		border-radius: 8rpx;
		padding: 20rpx 30rpx;
		font-size: 28rpx;
		text-align: center;
	}

	.btn-default {
		background-color: #f5f5f5;
		color: #333;
		border-radius: 8rpx;
		padding: 20rpx 30rpx;
		font-size: 28rpx;
		text-align: center;
	}

	.text-primary {
		color: #007AFF;
	}

	.text-success {
		color: #4cd964;
	}

	.text-warning {
		color: #f0ad4e;
	}

	.text-danger {
		color: #dd524d;
	}

	.text-muted {
		color: #8f8f94;
	}

	.text-center {
		text-align: center;
	}

	.text-bold {
		font-weight: bold;
	}

	.mt-10 {
		margin-top: 10rpx;
	}

	.mt-20 {
		margin-top: 20rpx;
	}

	.mb-10 {
		margin-bottom: 10rpx;
	}

	.mb-20 {
		margin-bottom: 20rpx;
	}
</style>

// 模拟农机数据（包含新的注册信息字段）
export const mockMachines = [
	{
		id: 1,
		// 新增的农机注册信息字段
		fname: '抛秧机001号',
		guige: 'PS-2024-A1',
		owner: '张三',
		company: '湖南农机制造有限公司',
		brand: '湘农牌',
		mobile: '13800138001',
		memo: '新购置设备，性能优良',
		// 原有的作业相关信息（保持兼容性）
		orderNjname: '抛秧机001号',
		orderName: '水稻抛秧作业',
		orderMj: '120.5',
		orderTime: '2023-05-15',
		orderSj: '张三',
		orderJw: '113.087030,28.202149',
		orderKs: 256,
		orderWhere: '湖南省长沙市岳麓区农田A区',
		orderMemo: '天气良好，作业顺利'
	},
	{
		id: 2,
		// 新增的农机注册信息字段
		fname: '抛秧机002号',
		guige: 'PS-2024-B2',
		owner: '李四',
		company: '江苏农机科技股份有限公司',
		brand: '苏农牌',
		mobile: '13800138002',
		memo: '设备状态良好，定期保养',
		// 原有的作业相关信息（保持兼容性）
		orderNjname: '抛秧机002号',
		orderName: '水稻抛秧作业',
		orderMj: '85.3',
		orderTime: '2023-05-15',
		orderSj: '李四',
		orderJw: '113.097030,28.212149',
		orderKs: 178,
		orderWhere: '湖南省长沙市岳麓区农田B区',
		orderMemo: '土壤湿润，需注意机械保养'
	},
	{
		id: 3,
		// 新增的农机注册信息字段
		fname: '抛秧机003号',
		guige: 'PS-2023-C3',
		owner: '王五',
		company: '山东农机集团有限公司',
		brand: '鲁农牌',
		mobile: '13800138003',
		memo: '维护保养中，暂停使用',
		// 原有的作业相关信息（保持兼容性）
		orderNjname: '抛秧机003号',
		orderName: '维护保养',
		orderMj: '0',
		orderTime: '2023-05-14',
		orderSj: '王五',
		orderJw: '113.077030,28.192149',
		orderKs: 0,
		orderWhere: '维修站',
		orderMemo: '定期保养，更换滤芯'
	},
	{
		id: 4,
		// 新增的农机注册信息字段
		fname: '抛秧机004号',
		guige: 'PS-2024-D4',
		owner: '赵六',
		company: '河南农机制造有限公司',
		brand: '豫农牌',
		mobile: '13800138004',
		memo: '高效作业设备，适合大面积作业',
		// 原有的作业相关信息（保持兼容性）
		orderNjname: '抛秧机004号',
		orderName: '水稻抛秧作业',
		orderMj: '65.8',
		orderTime: '2023-05-15',
		orderSj: '赵六',
		orderJw: '113.107030,28.222149',
		orderKs: 143,
		orderWhere: '湖南省长沙市岳麓区农田C区',
		orderMemo: '秧苗成活率高'
	},
	{
		id: 5,
		// 新增的农机注册信息字段
		fname: '抛秧机005号',
		guige: 'PS-2024-E5',
		owner: '孙七',
		company: '广东农机科技有限公司',
		brand: '粤农牌',
		mobile: '13800138005',
		memo: '节能环保型设备，燃油消耗低',
		// 原有的作业相关信息（保持兼容性）
		orderNjname: '抛秧机005号',
		orderName: '水稻抛秧作业',
		orderMj: '95.2',
		orderTime: '2023-05-15',
		orderSj: '孙七',
		orderJw: '113.117030,28.232149',
		orderKs: 201,
		orderWhere: '湖南省长沙市岳麓区农田D区',
		orderMemo: '抛秧效果良好，质量优秀'
	},
	{
		id: 6,
		// 新增的农机注册信息字段
		fname: '抛秧机006号',
		guige: 'PS-2024-F6',
		owner: '陈八',
		company: '浙江农机制造股份有限公司',
		brand: '浙农牌',
		mobile: '13800138006',
		memo: '智能化程度高，操作简便',
		// 原有的作业相关信息（保持兼容性）
		orderNjname: '抛秧机006号',
		orderName: '水稻抛秧作业',
		orderMj: '156.7',
		orderTime: '2023-05-16',
		orderSj: '陈八',
		orderJw: '113.127030,28.242149',
		orderKs: 312,
		orderWhere: '湖南省长沙市岳麓区农田E区',
		orderMemo: '大面积作业，效率高'
	},
	{
		id: 7,
		// 新增的农机注册信息字段
		fname: '抛秧机007号',
		guige: 'PS-2023-G7',
		owner: '刘九',
		company: '四川农机工业有限公司',
		brand: '川农牌',
		mobile: '13800138007',
		memo: '适应性强，可在多种地形作业',
		// 原有的作业相关信息（保持兼容性）
		orderNjname: '抛秧机007号',
		orderName: '水稻抛秧作业',
		orderMj: '78.9',
		orderTime: '2023-05-16',
		orderSj: '刘九',
		orderJw: '113.137030,28.252149',
		orderKs: 167,
		orderWhere: '湖南省长沙市岳麓区农田F区',
		orderMemo: '秧苗品质优良，无病虫害'
	},
	{
		id: 8,
		// 新增的农机注册信息字段
		fname: '抛秧机008号',
		guige: 'PS-2024-H8',
		owner: '周十',
		company: '安徽农机科技有限公司',
		brand: '皖农牌',
		mobile: '13800138008',
		memo: '维护保养中，预计明日恢复使用',
		// 原有的作业相关信息（保持兼容性）
		orderNjname: '抛秧机008号',
		orderName: '维护保养',
		orderMj: '0',
		orderTime: '2023-05-16',
		orderSj: '周十',
		orderJw: '113.147030,28.262149',
		orderKs: 0,
		orderWhere: '维修站',
		orderMemo: '更换液压油，检查传动系统'
	},
	{
		id: 9,
		// 新增的农机注册信息字段
		fname: '抛秧机009号',
		guige: 'PS-2024-I9',
		owner: '吴十一',
		company: '江西农机制造有限公司',
		brand: '赣农牌',
		mobile: '13800138009',
		memo: '新型号设备，抛秧精度高',
		// 原有的作业相关信息（保持兼容性）
		orderNjname: '抛秧机009号',
		orderName: '水稻抛秧作业',
		orderMj: '112.4',
		orderTime: '2023-05-16',
		orderSj: '吴十一',
		orderJw: '113.157030,28.272149',
		orderKs: 234,
		orderWhere: '湖南省长沙市岳麓区农田G区',
		orderMemo: '秧苗间距均匀，抛秧顺利'
	},
	{
		id: 10,
		// 新增的农机注册信息字段
		fname: '抛秧机010号',
		guige: 'PS-2024-J10',
		owner: '郑十二',
		company: '福建农机工业股份有限公司',
		brand: '闽农牌',
		mobile: '13800138010',
		memo: '经济实用型设备，性价比高',
		// 原有的作业相关信息（保持兼容性）
		orderNjname: '抛秧机010号',
		orderName: '水稻抛秧作业',
		orderMj: '89.6',
		orderTime: '2023-05-17',
		orderSj: '郑十二',
		orderJw: '113.167030,28.282149',
		orderKs: 189,
		orderWhere: '湖南省长沙市岳麓区农田H区',
		orderMemo: '秧苗成活率高，产量可观'
	}
];

// 为了保持向后兼容，保留原有的 mockOrders 导出
export const mockOrders = mockMachines;

// 模拟工作历史记录（基于订单数据）
export const mockWorkHistory = [
	{
		id: 'h001',
		orderId: 1,
		date: '2023-05-15',
		startTime: '08:30:00',
		endTime: '17:30:00',
		workArea: 120.5,
		packageCount: 256,
		locations: [
			{ longitude: 113.087030, latitude: 28.202149, timestamp: '2023-05-15 08:30:00' },
			{ longitude: 113.089030, latitude: 28.200149, timestamp: '2023-05-15 10:30:00' },
			{ longitude: 113.091030, latitude: 28.198149, timestamp: '2023-05-15 12:30:00' },
			{ longitude: 113.093030, latitude: 28.196149, timestamp: '2023-05-15 14:30:00' },
			{ longitude: 113.095030, latitude: 28.194149, timestamp: '2023-05-15 17:30:00' }
		]
	},
	{
		id: 'h002',
		orderId: 2,
		date: '2023-05-15',
		startTime: '09:00:00',
		endTime: '18:00:00',
		workArea: 85.3,
		packageCount: 178,
		locations: [
			{ longitude: 113.097030, latitude: 28.212149, timestamp: '2023-05-15 09:00:00' },
			{ longitude: 113.099030, latitude: 28.214149, timestamp: '2023-05-15 11:00:00' },
			{ longitude: 113.101030, latitude: 28.216149, timestamp: '2023-05-15 13:00:00' },
			{ longitude: 113.103030, latitude: 28.218149, timestamp: '2023-05-15 15:00:00' },
			{ longitude: 113.105030, latitude: 28.220149, timestamp: '2023-05-15 18:00:00' }
		]
	},
	{
		id: 'h003',
		orderId: 4,
		date: '2023-05-15',
		startTime: '07:45:00',
		endTime: '16:45:00',
		workArea: 65.8,
		packageCount: 143,
		locations: [
			{ longitude: 113.107030, latitude: 28.222149, timestamp: '2023-05-15 07:45:00' },
			{ longitude: 113.109030, latitude: 28.224149, timestamp: '2023-05-15 10:45:00' },
			{ longitude: 113.111030, latitude: 28.226149, timestamp: '2023-05-15 13:45:00' },
			{ longitude: 113.113030, latitude: 28.228149, timestamp: '2023-05-15 16:45:00' }
		]
	},
	{
		id: 'h004',
		orderId: 5,
		date: '2023-05-15',
		startTime: '08:15:00',
		endTime: '17:15:00',
		workArea: 95.2,
		packageCount: 201,
		locations: [
			{ longitude: 113.117030, latitude: 28.232149, timestamp: '2023-05-15 08:15:00' },
			{ longitude: 113.119030, latitude: 28.234149, timestamp: '2023-05-15 11:15:00' },
			{ longitude: 113.121030, latitude: 28.236149, timestamp: '2023-05-15 14:15:00' },
			{ longitude: 113.123030, latitude: 28.238149, timestamp: '2023-05-15 17:15:00' }
		]
	},
	{
		id: 'h005',
		orderId: 6,
		date: '2023-05-16',
		startTime: '07:30:00',
		endTime: '18:30:00',
		workArea: 156.7,
		packageCount: 312,
		locations: [
			{ longitude: 113.127030, latitude: 28.242149, timestamp: '2023-05-16 07:30:00' },
			{ longitude: 113.129030, latitude: 28.244149, timestamp: '2023-05-16 10:30:00' },
			{ longitude: 113.131030, latitude: 28.246149, timestamp: '2023-05-16 13:30:00' },
			{ longitude: 113.133030, latitude: 28.248149, timestamp: '2023-05-16 16:30:00' },
			{ longitude: 113.135030, latitude: 28.250149, timestamp: '2023-05-16 18:30:00' }
		]
	},
	{
		id: 'h006',
		orderId: 7,
		date: '2023-05-16',
		startTime: '08:45:00',
		endTime: '17:00:00',
		workArea: 78.9,
		packageCount: 165,
		locations: [
			{ longitude: 113.137030, latitude: 28.252149, timestamp: '2023-05-16 08:45:00' },
			{ longitude: 113.139030, latitude: 28.254149, timestamp: '2023-05-16 11:45:00' },
			{ longitude: 113.141030, latitude: 28.256149, timestamp: '2023-05-16 14:45:00' },
			{ longitude: 113.143030, latitude: 28.258149, timestamp: '2023-05-16 17:00:00' }
		]
	},
	{
		id: 'h007',
		orderId: 9,
		date: '2023-05-16',
		startTime: '07:00:00',
		endTime: '18:00:00',
		workArea: 112.4,
		packageCount: 234,
		locations: [
			{ longitude: 113.157030, latitude: 28.272149, timestamp: '2023-05-16 07:00:00' },
			{ longitude: 113.159030, latitude: 28.274149, timestamp: '2023-05-16 10:00:00' },
			{ longitude: 113.161030, latitude: 28.276149, timestamp: '2023-05-16 13:00:00' },
			{ longitude: 113.163030, latitude: 28.278149, timestamp: '2023-05-16 16:00:00' },
			{ longitude: 113.165030, latitude: 28.280149, timestamp: '2023-05-16 18:00:00' }
		]
	},
	{
		id: 'h008',
		orderId: 10,
		date: '2023-05-16',
		startTime: '08:20:00',
		endTime: '17:20:00',
		workArea: 89.6,
		packageCount: 189,
		locations: [
			{ longitude: 113.167030, latitude: 28.282149, timestamp: '2023-05-16 08:20:00' },
			{ longitude: 113.169030, latitude: 28.284149, timestamp: '2023-05-16 11:20:00' },
			{ longitude: 113.171030, latitude: 28.286149, timestamp: '2023-05-16 14:20:00' },
			{ longitude: 113.173030, latitude: 28.288149, timestamp: '2023-05-16 17:20:00' }
		]
	}
];

// 模拟获取作业订单列表API
export const mockGetOrderList = (userInfo) => {
	return new Promise((resolve) => {
		setTimeout(() => {
			let accessibleOrders = [];
			
			// 根据用户权限返回可访问的订单列表
			if (userInfo.role === 'admin' || userInfo.accessMachines === 'all') {
				accessibleOrders = mockOrders;
			} else {
				// 普通用户只能看到部分订单
				accessibleOrders = mockOrders.filter(order => 
					order.orderSj === userInfo.username || 
					(userInfo.accessMachines && userInfo.accessMachines.includes(order.id.toString()))
				);
			}
			
			resolve({
				code: 200,
				message: '获取作业订单列表成功',
				data: accessibleOrders
			});
		}, 800);
	});
};

// 模拟获取单个订单详情API
export const mockGetOrderDetail = (orderId, userInfo) => {
	return new Promise((resolve, reject) => {
		setTimeout(() => {
			// 检查用户是否有权限访问该订单
			const hasAccess = userInfo.role === 'admin' || 
							  userInfo.accessMachines === 'all';
			
			if (!hasAccess) {
				const order = mockOrders.find(o => o.id.toString() === orderId.toString());
				if (!order || (order.orderSj !== userInfo.username && 
					(!userInfo.accessMachines || !userInfo.accessMachines.includes(orderId.toString())))) {
					resolve({
						code: 403,
						message: '无权限访问该订单',
						data: null
					});
					return;
				}
			}
			
			const order = mockOrders.find(o => o.id.toString() === orderId.toString());
			
			if (order) {
				resolve({
					code: 200,
					message: '获取订单详情成功',
					data: order
				});
			} else {
				resolve({
					code: 404,
					message: '订单不存在',
					data: null
				});
			}
		}, 500);
	});
};

// 模拟获取工作历史API
export const mockGetWorkHistory = (userInfo, orderId = null) => {
	return new Promise((resolve) => {
		setTimeout(() => {
			let history = mockWorkHistory;
			
			// 如果指定了订单ID，只返回该订单的历史记录
			if (orderId) {
				history = history.filter(h => h.orderId.toString() === orderId.toString());
			}
			
			// 根据用户权限过滤历史记录
			if (userInfo.role !== 'admin' && userInfo.accessMachines !== 'all') {
				const accessibleOrderIds = mockOrders
					.filter(order => order.orderSj === userInfo.username || 
						(userInfo.accessMachines && userInfo.accessMachines.includes(order.id.toString())))
					.map(order => order.id);
				
				history = history.filter(h => accessibleOrderIds.includes(h.orderId));
			}
			
			resolve({
				code: 200,
				message: '获取工作历史成功',
				data: history
			});
		}, 600);
	});
};

// 兼容旧的API名称
export const mockGetMachineList = mockGetOrderList;
export const mockGetMachineDetail = mockGetOrderDetail;
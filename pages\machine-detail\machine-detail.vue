<template>
	<view class="container">
		<!-- 美化的头部区域 -->
		<view class="hero-header" :class="machine.status">
			<view class="header-background"></view>
			<view class="header-content">
				<view class="back-btn" @click="goBack">
					<text class="back-icon">‹</text>
				</view>

				<view class="machine-hero-info">
					<view class="machine-title-section">
						<text class="machine-name">{{ getMachineInfo('fname') || machine.fname || '加载中...' }}</text>
						<text class="machine-model">{{ getMachineInfo('guige') || machine.guige || '未知型号' }}</text>
					</view>

					<view class="status-indicator">
						<view class="status-dot" :class="machine.status"></view>
						<text class="status-text">{{ getStatusText(machine.status) }}</text>
					</view>
				</view>
			</view>
		</view>

		<view class="content-section">
			<!-- 核心数据展示卡片 -->
			<view class="stats-grid">
				<view class="stat-card primary">
					<view class="stat-icon">🌾</view>
					<view class="stat-content">
						<text class="stat-value">{{ machine.workArea || 0 }}</text>
						<text class="stat-unit">亩</text>
						<text class="stat-label">作业面积</text>
					</view>
					<view class="stat-trend">
						<text class="trend-text">今日</text>
					</view>
				</view>

				<view class="stat-card secondary">
					<view class="stat-icon">📦</view>
					<view class="stat-content">
						<text class="stat-value">{{ machine.packageCount || 0 }}</text>
						<text class="stat-unit">个</text>
						<text class="stat-label">打捆数量</text>
					</view>
					<view class="stat-trend">
						<text class="trend-text">累计</text>
					</view>
				</view>
			</view>
			
			<!-- 农机详细信息卡片 -->
			<view class="info-card modern">
				<view class="card-header">
					<view class="card-icon">🚜</view>
					<text class="card-title">农机信息</text>
				</view>

				<view class="info-grid">
					<view class="info-group">
						<view class="info-item">
							<view class="info-icon">🏷️</view>
							<view class="info-content">
								<text class="info-label">农机ID</text>
								<text class="info-value">{{ machine.id || '--' }}</text>
							</view>
						</view>

						<view class="info-item">
							<view class="info-icon">🚜</view>
							<view class="info-content">
								<text class="info-label">农机名称</text>
								<text class="info-value">{{ getMachineInfo('fname') }}</text>
							</view>
						</view>

						<view class="info-item">
							<view class="info-icon">⚙️</view>
							<view class="info-content">
								<text class="info-label">规格型号</text>
								<text class="info-value">{{ getMachineInfo('guige') }}</text>
							</view>
						</view>
					</view>

					<view class="info-group">
						<view class="info-item">
							<view class="info-icon">👨‍💼</view>
							<view class="info-content">
								<text class="info-label">车主姓名</text>
								<text class="info-value">{{ getMachineInfo('owner') }}</text>
							</view>
						</view>

						<view class="info-item">
							<view class="info-icon">🏭</view>
							<view class="info-content">
								<text class="info-label">生产厂家</text>
								<text class="info-value">{{ getMachineInfo('company') }}</text>
							</view>
						</view>

						<view class="info-item">
							<view class="info-icon">🏷️</view>
							<view class="info-content">
								<text class="info-label">品牌</text>
								<text class="info-value">{{ getMachineInfo('brand') }}</text>
							</view>
						</view>
					</view>

					<view class="info-group">
						<view class="info-item">
							<view class="info-icon">📱</view>
							<view class="info-content">
								<text class="info-label">联系电话</text>
								<text class="info-value phone">{{ formatMobileNumber(getMachineInfo('mobile')) }}</text>
							</view>
						</view>

						<view class="info-item full-width" v-if="getMachineInfo('memo')">
							<view class="info-icon">📝</view>
							<view class="info-content">
								<text class="info-label">备注信息</text>
								<text class="info-value memo">{{ getMachineInfo('memo') }}</text>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 作业管理操作区域 -->
			<view class="action-card modern">
				<view class="card-header">
					<view class="card-icon">⚡</view>
					<text class="card-title">作业管理</text>
				</view>

				<view class="action-grid">
					<view class="action-item primary" @click="showCreateJobModal">
						<view class="action-icon-wrapper">
							<text class="action-icon">+</text>
						</view>
						<view class="action-content">
							<text class="action-title">新增作业</text>
							<text class="action-desc">创建新的作业任务</text>
						</view>
						<view class="action-arrow">›</view>
					</view>

					<view class="action-item danger" @click="showDeleteJobConfirm">
						<view class="action-icon-wrapper">
							<text class="action-icon">×</text>
						</view>
						<view class="action-content">
							<text class="action-title">删除作业</text>
							<text class="action-desc">删除当前作业任务</text>
						</view>
						<view class="action-arrow">›</view>
					</view>
				</view>
			</view>

			<!-- 农机管理操作区域 -->
			<view class="action-card modern machine-management">
				<view class="card-header">
					<view class="card-icon">🚜</view>
					<text class="card-title">农机管理</text>
				</view>

				<view class="action-grid">
					<view class="action-item edit" @click="showEditMachineModal">
						<view class="action-icon-wrapper">
							<text class="action-icon">✏️</text>
						</view>
						<view class="action-content">
							<text class="action-title">修改信息</text>
							<text class="action-desc">编辑农机基本信息</text>
						</view>
						<view class="action-arrow">›</view>
					</view>

					<view class="action-item warning" @click="showDeleteMachineConfirm">
						<view class="action-icon-wrapper">
							<text class="action-icon">🗑️</text>
						</view>
						<view class="action-content">
							<text class="action-title">删除农机</text>
							<text class="action-desc">从系统中移除此农机</text>
						</view>
						<view class="action-arrow">›</view>
					</view>
				</view>
			</view>

			<view class="card location-card">
				<view class="card-title">当前位置</view>
				<view class="map-placeholder">
					<text>位置信息: {{ locationInfo }}</text>
				</view>
			</view>
			
			<view class="card history-card" v-if="machine.history && machine.history.length > 0">
				<view class="card-title">最近作业记录</view>
				<view class="history-list">
					<view class="history-item" v-for="(item, index) in recentHistory" :key="item.id">
						<view class="history-date">{{ item.date }}</view>
						<view class="history-detail">
							<view class="history-time">{{ item.startTime }} - {{ item.endTime }}</view>
							<view class="history-data">
								<text>作业面积: {{ item.workArea }}亩</text>
								<text>打捆数量: {{ item.packageCount }}个</text>
							</view>
						</view>
					</view>
				</view>
				
				<view class="view-more" @click="goToHistory" v-if="machine.history.length > 3">
					<text>查看更多</text>
				</view>
			</view>
		</view>

		<!-- 创建作业弹窗 -->
		<view class="modal-overlay" v-if="showCreateJobForm" @click="hideCreateJobModal">
			<view class="modal-content" @click.stop>
				<view class="modal-header">
					<text class="modal-title">新增作业</text>
					<view class="modal-close" @click="hideCreateJobModal">
						<text>×</text>
					</view>
				</view>

				<view class="modal-body">
					<view class="form-group">
						<text class="form-label">作业名称</text>
						<input
							class="form-input"
							v-model="createJobForm.jobName"
							placeholder="请输入作业名称（可选）"
							maxlength="50"
						/>
						<text class="form-hint">如不填写将自动生成作业名称</text>
					</view>

					<view class="form-group">
						<text class="form-label">作业地点 *</text>
						<view class="location-input-group">
							<input
								class="form-input location-input"
								v-model="createJobForm.workLocation"
								placeholder="请输入作业地点"
								maxlength="100"
							/>
							<view class="location-btn" @click="getCurrentLocation" :class="{ 'loading': locationLoading }">
								<text class="location-icon" v-if="!locationLoading">📍</text>
								<text class="location-icon" v-else>🔄</text>
								<text class="location-text">{{ locationLoading ? '定位中' : '定位' }}</text>
							</view>
						</view>
						<text class="form-hint" v-if="currentLocationInfo">{{ currentLocationInfo }}</text>
						<text class="form-hint" v-else>点击定位按钮获取当前位置</text>
					</view>
				</view>

				<view class="modal-footer enhanced">
					<view class="btn btn-cancel" @click="hideCreateJobModal">
						<text>取消</text>
					</view>
					<view class="btn btn-primary" @click="submitCreateJob" :class="{ 'btn-loading': createJobLoading }">
						<text v-if="!createJobLoading">创建作业</text>
						<text v-else>创建中...</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 修改农机信息弹窗 -->
		<view class="modal-overlay" v-if="showEditMachineForm" @click="hideEditMachineModal">
			<view class="modal-content large" @click.stop>
				<view class="modal-header">
					<text class="modal-title">修改农机信息</text>
					<view class="modal-close" @click="hideEditMachineModal">
						<text>×</text>
					</view>
				</view>

				<view class="modal-body">
					<view class="form-group">
						<text class="form-label">农机名称 *</text>
						<input
							class="form-input"
							v-model="editMachineForm.fname"
							placeholder="请输入农机名称"
							maxlength="50"
						/>
					</view>

					<view class="form-group">
						<text class="form-label">规格型号 *</text>
						<input
							class="form-input"
							v-model="editMachineForm.guige"
							placeholder="请输入规格型号"
							maxlength="50"
						/>
					</view>

					<view class="form-group">
						<text class="form-label">车主姓名 *</text>
						<input
							class="form-input"
							v-model="editMachineForm.owner"
							placeholder="请输入车主姓名"
							maxlength="20"
						/>
					</view>

					<view class="form-group">
						<text class="form-label">生产厂家 *</text>
						<input
							class="form-input"
							v-model="editMachineForm.company"
							placeholder="请输入生产厂家"
							maxlength="50"
						/>
					</view>

					<view class="form-group">
						<text class="form-label">品牌 *</text>
						<input
							class="form-input"
							v-model="editMachineForm.brand"
							placeholder="请输入品牌"
							maxlength="30"
						/>
					</view>

					<view class="form-group">
						<text class="form-label">联系电话 *</text>
						<input
							class="form-input"
							v-model="editMachineForm.mobile"
							placeholder="请输入联系电话"
							type="number"
							maxlength="11"
						/>
					</view>

					<view class="form-group">
						<text class="form-label">备注信息</text>
						<textarea
							class="form-textarea"
							v-model="editMachineForm.memo"
							placeholder="请输入备注信息（可选）"
							maxlength="200"
						/>
					</view>
				</view>

				<view class="modal-footer">
					<view class="btn btn-cancel" @click="hideEditMachineModal">
						<text>取消</text>
					</view>
					<view class="btn btn-primary" @click="submitEditMachine" :class="{ 'btn-loading': editMachineLoading }">
						<text v-if="!editMachineLoading">保存修改</text>
						<text v-else>保存中...</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 删除农机确认弹窗 -->
		<view class="modal-overlay" v-if="showDeleteMachineForm" @click="hideDeleteMachineModal">
			<view class="modal-content" @click.stop>
				<view class="modal-header danger">
					<text class="modal-title">⚠️ 删除确认</text>
				</view>

				<view class="modal-body">
					<view class="warning-content">
						<view class="warning-icon">🚨</view>
						<text class="warning-text">确定要删除农机"{{ getMachineInfo('fname') }}"吗？</text>
						<text class="warning-desc">删除后将无法恢复，请谨慎操作！</text>
					</view>
				</view>

				<view class="modal-footer">
					<view class="btn btn-cancel" @click="hideDeleteMachineModal">
						<text>取消</text>
					</view>
					<view class="btn btn-danger" @click="submitDeleteMachine" :class="{ 'btn-loading': deleteMachineLoading }">
						<text v-if="!deleteMachineLoading">确认删除</text>
						<text v-else>删除中...</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { getUserInfo } from '@/common/utils/auth.js';
import { getMachineDetail, createJob, deleteJob, updateMachine, deleteMachine, getMachineListNew } from '@/common/api/machines.js';

export default {
	data() {
		return {
			machineId: '',
			userInfo: null,
			machine: {
				id: '',
				fname: '',
				guige: '',
				owner: '',
				company: '',
				brand: '',
				mobile: '',
				memo: '',
				jw: '',
				status: 'idle',
				workArea: 0,
				packageCount: 0,
				driver: {
					name: '',
					phone: ''
				},
				lastActiveTime: '',
				originalMachine: {},
				originalOrder: {},
				history: []
			},
			loading: false,
			// 作业管理相关数据
			showCreateJobForm: false,
			createJobLoading: false,
			createJobForm: {
				jobName: '',
				workLocation: '' // 作业地点
			},
			// 定位相关
			locationLoading: false,
			currentLocationInfo: '',
			currentCoordinates: '', // 当前经纬度
			// 农机管理相关数据
			showEditMachineForm: false,
			editMachineLoading: false,
			editMachineForm: {
				fname: '',
				guige: '',
				owner: '',
				company: '',
				brand: '',
				mobile: '',
				memo: ''
			},
			showDeleteMachineForm: false,
			deleteMachineLoading: false
		}
	},
	computed: {
		locationInfo() {
			// 优先从真实的后端数据获取位置信息
			if (this.machine.jw) {
				const coords = this.machine.jw.split(',');
				if (coords.length === 2) {
					return `经度 ${coords[0]}, 纬度 ${coords[1]}`;
				}
			}
			
			// 兼容原来的location格式
			if (this.machine.location) {
				return `经度 ${this.machine.location.longitude}, 纬度 ${this.machine.location.latitude}`;
			}
			
			// 从originalOrder获取位置信息
			if (this.machine.originalOrder && this.machine.originalOrder.orderJw) {
				const coords = this.machine.originalOrder.orderJw.split(',');
				if (coords.length === 2) {
					return `经度 ${coords[0]}, 纬度 ${coords[1]}`;
				}
			}
			
			return '暂无位置信息';
		},
		recentHistory() {
			if (!this.machine.history) return [];
			// 只显示最近3条记录
			return this.machine.history.slice(0, 3);
		}
	},
	onLoad(option) {
		try {
			if (option.id) {
				this.machineId = option.id;
			}

			this.userInfo = getUserInfo();
			
			// 使用$nextTick确保组件完全初始化后再加载数据
			this.$nextTick(() => {
				this.loadMachineDetail();
			});
		} catch (error) {
			console.error('❌ onLoad执行时发生错误:', error);
		}
	},
	methods: {
		loadMachineDetail() {
			try {
				// 直接从全局数据获取农机信息，不再请求后端
				const globalData = getApp().globalData;
				console.log('🔍 检查全局数据:', globalData);
				
				if (globalData && globalData.currentMachine) {
					const machineData = globalData.currentMachine;
					console.log('🚜 农机详情页面加载数据:', machineData);
					
					// 使用Object.assign()安全地更新响应式对象，而不是直接赋值
					Object.assign(this.machine, {
						id: machineData.id || '',
						fname: machineData.fname || '',
						guige: machineData.guige || '',
						owner: machineData.owner || '',
						company: machineData.company || '',
						brand: machineData.brand || '',
						mobile: machineData.mobile || '',
						memo: machineData.memo || '',
						jw: machineData.jw || '',
						status: machineData.status || 'idle',
						workArea: machineData.workArea || 0,
						packageCount: machineData.packageCount || 0,
						driver: machineData.driver || {},
						lastActiveTime: machineData.lastActiveTime || '',
						originalMachine: machineData.originalMachine || {},
						originalOrder: machineData.originalOrder || {},
						history: machineData.history || []
					});
					
					console.log('🔍 农机基本信息检查:');
					console.log('- fname:', this.machine.fname);
					console.log('- guige:', this.machine.guige);
					console.log('- owner:', this.machine.owner);
					console.log('- company:', this.machine.company);
					console.log('- brand:', this.machine.brand);
					console.log('- mobile:', this.machine.mobile);
					console.log('- status:', this.machine.status);
					
					this.loading = false;
					
					// 使用$nextTick确保DOM更新完成后再执行测试
					this.$nextTick(() => {
						this.testMachineData();
					});
					return;
				}
				
				// 如果没有全局数据，提示并返回
				console.warn('⚠️ 未找到农机数据，可能是直接访问详情页面');
				uni.showToast({
					title: '农机数据加载失败',
					icon: 'none'
				});
				setTimeout(() => {
					this.goBack();
				}, 1500);
				
			} catch (error) {
				console.error('❌ 加载农机详情数据时发生错误:', error);
				uni.showToast({
					title: '数据加载失败',
					icon: 'none'
				});
				setTimeout(() => {
					this.goBack();
				}, 1500);
			}
		},
		
		getStatusText(status) {
			const statusMap = {
				'working': '作业中',
				'idle': '空闲',
				'maintenance': '维护中'
			};
			
			return statusMap[status] || '未知';
		},
		
		goBack() {
			uni.navigateBack();
		},
		
		goToHistory() {
			uni.navigateTo({
				url: `/pages/history/history?machineId=${this.machineId}`
			});
		},

		// 获取农机信息字段
		getMachineInfo(field) {
			console.log('🔍 获取农机信息字段:', field, '值:', this.machine[field]);
			
			// 优先从农机主数据获取
			if (this.machine && this.machine[field] !== undefined && this.machine[field] !== null && this.machine[field] !== '') {
				return this.machine[field];
			}
			
			// 如果主数据没有，尝试从originalMachine获取
			if (this.machine && this.machine.originalMachine && this.machine.originalMachine[field] !== undefined && this.machine.originalMachine[field] !== null && this.machine.originalMachine[field] !== '') {
				return this.machine.originalMachine[field];
			}

			// 最后尝试从originalOrder获取（兼容旧格式）
			const originalOrder = this.machine.originalOrder;
			if (originalOrder) {
				switch (field) {
					case 'fname':
						return originalOrder.orderNjname || '--';
					case 'owner':
						return originalOrder.orderSj || '--';
					case 'mobile':
						return originalOrder.orderPhone || '--';
					case 'memo':
						return originalOrder.orderMemo || '';
					case 'guige':
						return originalOrder.orderGuige || '--';
					case 'company':
						return originalOrder.orderCompany || '--';
					case 'brand':
						return originalOrder.orderBrand || '--';
					default:
						return '--';
				}
			}

			return '--';
		},

		// 格式化手机号（脱敏显示）
		formatMobileNumber(mobile) {
			if (!mobile || mobile === '--') {
				return '--';
			}
			// 手机号脱敏显示: 138****8888
			if (mobile.length === 11) {
				return mobile.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
			}
			return mobile;
		},

		// 测试数据方法
		testMachineData() {
			console.log('🧪 测试农机数据:');
			console.log('整个machine对象:', this.machine);
			console.log('getMachineInfo测试:');
			console.log('- fname:', this.getMachineInfo('fname'));
			console.log('- guige:', this.getMachineInfo('guige'));
			console.log('- owner:', this.getMachineInfo('owner'));
			console.log('- company:', this.getMachineInfo('company'));
			console.log('- brand:', this.getMachineInfo('brand'));
			console.log('- mobile:', this.getMachineInfo('mobile'));
		},

		// 显示创建作业弹窗
		showCreateJobModal() {
			this.showCreateJobForm = true;
			this.createJobForm.jobName = '';
		},

		// 隐藏创建作业弹窗
		hideCreateJobModal() {
			this.showCreateJobForm = false;
			this.createJobForm.jobName = '';
			this.createJobForm.workLocation = '';
			this.currentLocationInfo = '';
			this.currentCoordinates = '';
			this.createJobLoading = false;
		},

		// 获取当前位置（使用设备GPS定位）
		async getCurrentLocation() {
			this.locationLoading = true;

			try {
				console.log('📍 开始获取当前位置...');
				
				const position = await new Promise((resolve, reject) => {
					uni.getLocation({
						type: 'wgs84', // 使用GPS原始坐标系，更通用
						geocode: true, // 启用地理编码
						altitude: true, // 获取海拔信息
						success: (res) => {
							console.log('📍 位置获取成功:', res);
							resolve(res);
						},
						fail: (err) => {
							console.error('📍 位置获取失败:', err);
							reject(err);
						}
					});
				});

				// 定位成功，保存坐标（注意：这里是WGS84坐标）
				this.currentCoordinates = `${position.longitude},${position.latitude}`;
				
				// 处理地址信息
				if (position.address) {
					this.currentLocationInfo = `当前位置：${position.address}`;
					if (!this.createJobForm.workLocation) {
						this.createJobForm.workLocation = position.address;
					}
				} else {
					// 如果没有地址信息，显示坐标
					this.currentLocationInfo = `当前坐标：${this.currentCoordinates}`;
					if (!this.createJobForm.workLocation) {
						this.createJobForm.workLocation = `坐标：${this.currentCoordinates}`;
					}
				}

				uni.showToast({
					title: '定位成功',
					icon: 'success'
				});

			} catch (error) {
				console.error('❌ 获取位置失败:', error);
				
				// 处理不同的错误情况
				let errorMessage = '定位失败';
				if (error.errMsg) {
					if (error.errMsg.includes('auth deny')) {
						errorMessage = '定位权限被拒绝，请在设置中开启定位权限';
					} else if (error.errMsg.includes('timeout')) {
						errorMessage = '定位超时，请检查网络连接';
					} else {
						errorMessage = '定位服务不可用';
					}
				}
				
				uni.showModal({
					title: '定位失败',
					content: errorMessage,
					showCancel: false,
					confirmText: '确定'
				});
			} finally {
				this.locationLoading = false;
			}
		},

		// 提交创建作业
		async submitCreateJob() {
			// 表单验证
			if (!this.createJobForm.workLocation.trim()) {
				uni.showToast({
					title: '请输入作业地点',
					icon: 'none'
				});
				return;
			}

			this.createJobLoading = true;

			try {
				console.log('📋 创建作业，提交数据:', {
					orderName: this.createJobForm.jobName,
					workLocation: this.createJobForm.workLocation,
					coordinates: this.currentCoordinates,
					machineInfo: this.machine
				});

				// 准备作业数据
				const jobData = {
					orderName: this.createJobForm.jobName.trim() || `${this.getMachineInfo('fname')}_作业_${Date.now()}`, // 后端要求用orderName而不是jobName
					orderWhere: this.createJobForm.workLocation.trim(), // 作业地点
					orderJw: this.currentCoordinates || '', // 作业经纬度
					orderSj: this.getMachineInfo('owner') || '待分配司机', // 作业司机
					orderNjname: this.getMachineInfo('fname') || '未知农机', // 作业农机名称
					orderTime: new Date().toISOString(), // 作业时间
					orderMemo: this.userInfo.username // 用username作为筛查该用户所有作业记录的标识符
				};

				const result = await createJob(this.userInfo, this.machineId, jobData);

				if (result.code === 200) {
					console.log('📋 作业创建成功:', result.data);

					// 后端返回成功但没有jobId，我们需要生成一个临时的jobId或使用其他标识
					// 检查后端是否返回了jobId
					const jobId = result.data?.jobId || result.data?.id || `job_${this.machineId}_${Date.now()}`;

					console.log('📋 使用的jobId:', jobId);

					// 将作业信息存储到全局数据中，供job-progress页面使用
					const globalData = getApp().globalData;
					globalData.currentJobInfo = {
						...jobData,
						jobId: jobId,
						machineInfo: this.machine
					};

					uni.showToast({
						title: '作业创建成功',
						icon: 'success'
					});

					this.hideCreateJobModal();

					// 跳转到作业进行中页面
					setTimeout(() => {
						uni.navigateTo({
							url: `/pages/job-progress/job-progress?jobId=${jobId}&machineId=${this.machineId}`
						});
					}, 1000);
				} else if (result.code === 401) {
					// 认证失败
					uni.showModal({
						title: '认证失败',
						content: result.message || 'token已过期，请重新登录',
						showCancel: false,
						confirmText: '重新登录',
						success: () => {
							uni.reLaunch({
								url: '/pages/login/login'
							});
						}
					});
				} else {
					uni.showToast({
						title: result.message || '创建作业失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('❌ 创建作业失败:', error);
				uni.showToast({
					title: '创建作业失败，请重试',
					icon: 'none'
				});
			} finally {
				this.createJobLoading = false;
			}
		},

		// 显示删除作业确认
		showDeleteJobConfirm() {
			uni.showModal({
				title: '删除作业',
				content: '确定要删除当前作业吗？此操作不可恢复。',
				confirmText: '删除',
				confirmColor: '#dd524d',
				success: (res) => {
					if (res.confirm) {
						this.submitDeleteJob();
					}
				}
			});
		},

		// 提交删除作业
		async submitDeleteJob() {
			// 这里需要获取当前作业ID，暂时使用机器ID作为示例
			const jobId = this.machineId; // 实际应该是当前作业的ID

			try {
				uni.showLoading({
					title: '删除中...'
				});

				const result = await deleteJob(this.userInfo, jobId);

				if (result.code === 200) {
					uni.showToast({
						title: '作业删除成功',
						icon: 'success'
					});

					// 重新加载农机详情
					setTimeout(() => {
						this.loadMachineDetail();
					}, 1000);
				} else {
					uni.showToast({
						title: result.message || '删除作业失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('删除作业失败:', error);
				uni.showToast({
					title: '删除作业失败，请重试',
					icon: 'none'
				});
			} finally {
				uni.hideLoading();
			}
		},



		// 显示修改农机弹窗
		showEditMachineModal() {
			// 填充当前农机信息到表单
			this.editMachineForm = {
				fname: this.getMachineInfo('fname'),
				guige: this.getMachineInfo('guige'),
				owner: this.getMachineInfo('owner'),
				company: this.getMachineInfo('company'),
				brand: this.getMachineInfo('brand'),
				mobile: this.getMachineInfo('mobile'),
				memo: this.getMachineInfo('memo')
			};
			this.showEditMachineForm = true;
		},

		// 隐藏修改农机弹窗
		hideEditMachineModal() {
			this.showEditMachineForm = false;
			this.editMachineLoading = false;
		},

		// 提交修改农机信息
		async submitEditMachine() {
			// 验证必填字段
			if (!this.editMachineForm.fname || !this.editMachineForm.guige ||
				!this.editMachineForm.owner || !this.editMachineForm.company ||
				!this.editMachineForm.brand || !this.editMachineForm.mobile) {
				uni.showToast({
					title: '请填写所有必填字段',
					icon: 'none'
				});
				return;
			}

			// 验证手机号格式
			const phoneRegex = /^1[3-9]\d{9}$/;
			if (!phoneRegex.test(this.editMachineForm.mobile)) {
				uni.showToast({
					title: '请输入正确的手机号',
					icon: 'none'
				});
				return;
			}

			this.editMachineLoading = true;

			try {
				console.log('🚜 开始修改农机信息，农机ID:', this.machineId);
				console.log('🚜 修改数据:', this.editMachineForm);
				
				// 调用真实的修改农机API
				const result = await updateMachine(this.userInfo, this.machineId, this.editMachineForm);
				
				console.log('🚜 修改农机API响应:', result);
				
				if (result.code === 200) {
					// 修改成功，更新本地数据
					Object.assign(this.machine, {
						...this.editMachineForm,
						// 更新原始数据
						originalMachine: {
							...this.machine.originalMachine,
							...this.editMachineForm
						}
					});
					
					// 更新全局数据
					const globalData = getApp().globalData;
					if (globalData && globalData.currentMachine) {
						Object.assign(globalData.currentMachine, {
							...this.editMachineForm,
							originalMachine: {
								...globalData.currentMachine.originalMachine,
								...this.editMachineForm
							}
						});
					}

					// 显示成功提示
					uni.showModal({
						title: '修改成功',
						content: `农机"${this.editMachineForm.fname}"的信息已成功更新！`,
						showCancel: false,
						confirmText: '确定',
						success: () => {
							this.hideEditMachineModal();
						}
					});
				} else if (result.code === 401) {
					// 认证失败
					uni.showModal({
						title: '认证失败',
						content: result.message || 'token已过期，请重新登录',
						showCancel: false,
						confirmText: '重新登录',
						success: () => {
							uni.reLaunch({
								url: '/pages/login/login'
							});
						}
					});
				} else {
					// 其他错误
					uni.showModal({
						title: '修改失败',
						content: result.message || '修改农机信息失败，请稍后重试',
						showCancel: false,
						confirmText: '确定'
					});
				}
			} catch (error) {
				console.error('❌ 修改农机信息时发生错误:', error);
				uni.showModal({
					title: '修改失败',
					content: '网络错误或系统异常，请检查网络连接后重试',
					showCancel: false,
					confirmText: '确定'
				});
			} finally {
				this.editMachineLoading = false;
			}
		},

		// 显示删除农机确认弹窗
		showDeleteMachineConfirm() {
			this.showDeleteMachineForm = true;
		},

		// 隐藏删除农机弹窗
		hideDeleteMachineModal() {
			this.showDeleteMachineForm = false;
			this.deleteMachineLoading = false;
		},

		// 提交删除农机
		async submitDeleteMachine() {
			this.deleteMachineLoading = true;

			try {
				console.log('🚜 开始删除农机，农机ID:', this.machineId);
				
				// 调用真实的删除农机API
				const result = await deleteMachine(this.userInfo, this.machineId);
				
				console.log('🚜 删除农机API响应:', result);
				
				if (result.code === 200) {
					// 删除成功
					this.hideDeleteMachineModal();
					
					// 显示成功提示并返回列表页面
					uni.showModal({
						title: '删除成功',
						content: `农机"${this.getMachineInfo('fname')}"已成功删除！`,
						showCancel: false,
						confirmText: '返回列表',
						success: () => {
							// 清除全局数据
							const globalData = getApp().globalData;
							if (globalData) {
								globalData.currentMachine = null;
							}
							
							// 返回农机列表页面
							uni.navigateBack({
								delta: 1,
								success: () => {
									// 通知列表页面刷新数据
									uni.$emit('refreshMachineList');
								}
							});
						}
					});
				} else if (result.code === 401) {
					// 认证失败
					uni.showModal({
						title: '认证失败',
						content: result.message || 'token已过期，请重新登录',
						showCancel: false,
						confirmText: '重新登录',
						success: () => {
							uni.reLaunch({
								url: '/pages/login/login'
							});
						}
					});
				} else if (result.code === 403) {
					// 权限不足
					uni.showModal({
						title: '权限不足',
						content: result.message || '您没有权限删除此农机',
						showCancel: false,
						confirmText: '确定'
					});
				} else {
					// 其他错误
					uni.showModal({
						title: '删除失败',
						content: result.message || '删除农机失败，请稍后重试',
						showCancel: false,
						confirmText: '确定'
					});
				}
			} catch (error) {
				console.error('❌ 删除农机时发生错误:', error);
				uni.showModal({
					title: '删除失败',
					content: '网络错误或系统异常，请检查网络连接后重试',
					showCancel: false,
					confirmText: '确定'
				});
			} finally {
				this.deleteMachineLoading = false;
			}
		}
	}
}
</script>

<style>
.container {
	background: linear-gradient(180deg, #f5f7fa 0%, #c3cfe2 100%);
	min-height: 100vh;
}

/* 美化的头部区域 */
.hero-header {
	position: relative;
	padding: 60rpx 0 80rpx;
	overflow: hidden;
}

.header-background {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	opacity: 0.9;
}

.hero-header.working .header-background {
	background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

.hero-header.idle .header-background {
	background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

.hero-header.maintenance .header-background {
	background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}

.header-content {
	position: relative;
	z-index: 2;
	padding: 0 30rpx;
}

.back-btn {
	position: absolute;
	top: 0;
	left: 30rpx;
	width: 80rpx;
	height: 80rpx;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	backdrop-filter: blur(10rpx);
	transition: all 0.3s ease;
}

.back-btn:active {
	background: rgba(255, 255, 255, 0.3);
	transform: scale(0.95);
}

.back-icon {
	font-size: 48rpx;
	color: #fff;
	font-weight: bold;
}

.machine-hero-info {
	padding: 20rpx 30rpx 0 120rpx;
	color: #fff;
	min-height: 160rpx;
	display: flex;
	flex-direction: column;
	justify-content: center;
}

.machine-title-section {
	margin-bottom: 24rpx;
}

.machine-name {
	font-size: 48rpx;
	font-weight: 800;
	margin-bottom: 12rpx;
	display: block;
	text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
	letter-spacing: -1rpx;
}

.machine-model {
	font-size: 28rpx;
	opacity: 0.9;
	display: block;
	background: rgba(255, 255, 255, 0.2);
	padding: 8rpx 16rpx;
	border-radius: 16rpx;
	display: inline-block;
	backdrop-filter: blur(10rpx);
}

.status-indicator {
	display: flex;
	align-items: center;
	gap: 12rpx;
	margin-top: 16rpx;
	background: rgba(255, 255, 255, 0.15);
	padding: 12rpx 20rpx;
	border-radius: 24rpx;
	backdrop-filter: blur(10rpx);
	border: 1rpx solid rgba(255, 255, 255, 0.2);
	align-self: flex-start;
}

.status-dot {
	width: 20rpx;
	height: 20rpx;
	border-radius: 50%;
	background: #fff;
	box-shadow: 0 0 0 4rpx rgba(255, 255, 255, 0.3);
	animation: pulse 2s infinite;
}

@keyframes pulse {
	0%, 100% { opacity: 1; }
	50% { opacity: 0.7; }
}

.status-text {
	font-size: 28rpx;
	font-weight: 600;
	color: #fff;
}

/* 内容区域 */
.content-section {
	padding: 20rpx;
	margin-top: -40rpx;
	position: relative;
	z-index: 3;
}

/* 统计数据网格 */
.stats-grid {
	display: flex;
	gap: 20rpx;
	margin-bottom: 30rpx;
}

.stat-card {
	flex: 1;
	background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
	border-radius: 24rpx;
	padding: 32rpx;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid rgba(255, 255, 255, 0.8);
	position: relative;
	overflow: hidden;
	transition: all 0.3s ease;
}

.stat-card:active {
	transform: translateY(-4rpx);
	box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.12);
}

.stat-card.primary {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #fff;
}

.stat-card.secondary {
	background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
	color: #fff;
}

.stat-icon {
	font-size: 40rpx;
	margin-bottom: 16rpx;
	display: block;
}

.stat-content {
	margin-bottom: 16rpx;
}

.stat-value {
	font-size: 56rpx;
	font-weight: 800;
	line-height: 1;
	display: inline-block;
}

.stat-unit {
	font-size: 24rpx;
	font-weight: 500;
	margin-left: 8rpx;
	opacity: 0.8;
}

.stat-label {
	font-size: 24rpx;
	margin-top: 8rpx;
	display: block;
	opacity: 0.9;
}

.stat-trend {
	position: absolute;
	top: 24rpx;
	right: 24rpx;
}

.trend-text {
	font-size: 20rpx;
	opacity: 0.7;
	background: rgba(255, 255, 255, 0.2);
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
}

/* 现代化卡片样式 */
.info-card.modern, .action-card.modern {
	background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
	border-radius: 24rpx;
	padding: 0;
	margin-bottom: 30rpx;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid rgba(255, 255, 255, 0.8);
	overflow: hidden;
}

.card-header {
	display: flex;
	align-items: center;
	gap: 16rpx;
	padding: 32rpx 32rpx 24rpx;
	background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
	border-bottom: 1rpx solid rgba(0, 122, 255, 0.1);
}

.card-icon {
	font-size: 32rpx;
	width: 60rpx;
	height: 60rpx;
	background: linear-gradient(135deg, #007AFF, #0056CC);
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.3);
}

.card-title {
	font-size: 32rpx;
	font-weight: 700;
	color: #1a1a1a;
	letter-spacing: -0.5rpx;
}

/* 信息网格布局 */
.info-grid {
	padding: 32rpx;
}

.info-group {
	margin-bottom: 32rpx;
}

.info-group:last-child {
	margin-bottom: 0;
}

.info-item {
	display: flex;
	align-items: flex-start;
	gap: 16rpx;
	margin-bottom: 24rpx;
	padding: 20rpx;
	background: rgba(0, 122, 255, 0.02);
	border-radius: 16rpx;
	border: 1rpx solid rgba(0, 122, 255, 0.08);
	transition: all 0.3s ease;
}

.info-item:last-child {
	margin-bottom: 0;
}

.info-item.full-width {
	width: 100%;
}

.info-icon {
	font-size: 24rpx;
	width: 48rpx;
	height: 48rpx;
	background: linear-gradient(135deg, #007AFF, #0056CC);
	border-radius: 12rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-shrink: 0;
	margin-top: 2rpx;
}

.info-content {
	flex: 1;
	min-width: 0;
}

.info-label {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 8rpx;
	display: block;
	font-weight: 500;
}

.info-value {
	font-size: 28rpx;
	color: #1a1a1a;
	font-weight: 600;
	word-wrap: break-word;
	line-height: 1.4;
}

.info-value.phone {
	color: #007AFF;
}

.info-value.memo {
	color: #666;
	font-weight: 400;
	line-height: 1.6;
}

/* 操作网格布局 */
.action-grid {
	padding: 32rpx;
}

.action-item {
	display: flex;
	align-items: center;
	gap: 20rpx;
	padding: 24rpx;
	margin-bottom: 20rpx;
	border-radius: 20rpx;
	transition: all 0.3s ease;
	cursor: pointer;
}

.action-item:last-child {
	margin-bottom: 0;
}

.action-item.primary {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #fff;
	box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
}

.action-item.danger {
	background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
	color: #fff;
	box-shadow: 0 8rpx 24rpx rgba(255, 154, 158, 0.3);
}

.action-item.edit {
	background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
	color: #fff;
	box-shadow: 0 8rpx 24rpx rgba(17, 153, 142, 0.3);
}

.action-item.warning {
	background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
	color: #fff;
	box-shadow: 0 8rpx 24rpx rgba(255, 107, 107, 0.3);
}

/* 农机管理卡片样式 */
.machine-management {
	margin-top: 20rpx;
}

.action-item:active {
	transform: translateY(-2rpx) scale(0.98);
}

.action-icon-wrapper {
	width: 60rpx;
	height: 60rpx;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	backdrop-filter: blur(10rpx);
}

.action-icon {
	font-size: 32rpx;
	font-weight: bold;
}

.action-content {
	flex: 1;
}

.action-title {
	font-size: 32rpx;
	font-weight: 700;
	margin-bottom: 8rpx;
	display: block;
}

.action-desc {
	font-size: 24rpx;
	opacity: 0.9;
	display: block;
}

.action-arrow {
	font-size: 32rpx;
	opacity: 0.7;
	transition: all 0.3s ease;
}

.action-item:active .action-arrow {
	transform: translateX(4rpx);
}

/* 弹窗样式 */
.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
	padding: 40rpx;
}

.modal-content {
	background-color: #fff;
	border-radius: 24rpx;
	width: 100%;
	max-width: 600rpx;
	overflow: hidden;
	animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
	from {
		opacity: 0;
		transform: translateY(100rpx) scale(0.9);
	}
	to {
		opacity: 1;
		transform: translateY(0) scale(1);
	}
}

.modal-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 40rpx 40rpx 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.modal-close {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 30rpx;
	background-color: #f5f5f5;
	font-size: 40rpx;
	color: #666;
	transition: all 0.2s ease;
}

.modal-close:active {
	background-color: #e0e0e0;
	transform: scale(0.95);
}

.modal-body {
	padding: 40rpx;
}

.form-group {
	margin-bottom: 20rpx;
}

.form-label {
	display: block;
	font-size: 28rpx;
	color: #333;
	margin-bottom: 16rpx;
	font-weight: 500;
}

.form-input {
	width: 100%;
	height: 80rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 12rpx;
	padding: 0 24rpx;
	font-size: 28rpx;
	color: #333;
	background-color: #fff;
	transition: all 0.3s ease;
}

.form-input:focus {
	border-color: #007AFF;
	box-shadow: 0 0 0 4rpx rgba(0, 122, 255, 0.1);
}

.form-hint {
	display: block;
	font-size: 24rpx;
	color: #999;
	margin-top: 8rpx;
}

/* 定位输入框样式 */
.location-input-group {
	display: flex;
	align-items: stretch;
	gap: 16rpx;
}

.location-input {
	flex: 1;
}

.location-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 8rpx;
	padding: 0 24rpx;
	background: linear-gradient(135deg, #667eea, #764ba2);
	border-radius: 12rpx;
	min-width: 120rpx;
	transition: all 0.3s ease;
	box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}

.location-btn:active {
	transform: scale(0.98);
	box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.4);
}

.location-btn.loading {
	background: linear-gradient(135deg, #94a3b8, #64748b);
	pointer-events: none;
}

.location-icon {
	font-size: 28rpx;
	color: #fff;
}

.location-btn.loading .location-icon {
	animation: rotate 1s linear infinite;
}

.location-text {
	font-size: 28rpx;
	color: #fff;
	font-weight: 600;
}

@keyframes rotate {
	from { transform: rotate(0deg); }
	to { transform: rotate(360deg); }
}

.modal-footer {
	display: flex;
	gap: 20rpx;
	padding: 20rpx 40rpx 40rpx;
	border-top: 1rpx solid #f0f0f0;
}

.btn {
	flex: 1;
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 12rpx;
	font-size: 28rpx;
	font-weight: 500;
	transition: all 0.3s ease;
}

.btn-cancel {
	background-color: #f5f5f5;
	color: #666;
}

.btn-cancel:active {
	background-color: #e0e0e0;
	transform: scale(0.98);
}

.btn-loading {
	opacity: 0.7;
	pointer-events: none;
}

.map-placeholder {
	height: 300rpx;
	background-color: #f5f5f5;
	border-radius: 8rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 28rpx;
	color: #999;
}

.history-list {
	margin-bottom: 20rpx;
}

.history-item {
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f5f5f5;
}

.history-item:last-child {
	border-bottom: none;
}

.history-date {
	font-size: 24rpx;
	color: #999;
	margin-bottom: 10rpx;
}

.history-detail {
	display: flex;
	justify-content: space-between;
}

.history-time {
	font-size: 28rpx;
	color: #333;
}

.history-data {
	font-size: 28rpx;
	color: #666;
	text-align: right;
}

.history-data text {
	display: block;
	margin-bottom: 5rpx;
}

.view-more {
	text-align: center;
	padding: 20rpx 0;
	color: #007AFF;
	font-size: 28rpx;
}

/* 增强的弹窗样式 */
.modal-content.large {
	max-width: 90%;
	max-height: 80vh;
	overflow-y: auto;
}

.modal-footer.enhanced {
	display: flex;
	gap: 15rpx;
	padding: 20rpx 40rpx 40rpx;
	border-top: 1rpx solid #f0f0f0;
}

.modal-footer.enhanced .btn {
	flex: 1;
	min-width: 120rpx;
}

.btn-secondary {
	background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
	color: #333;
	border: 1rpx solid #fcb69f;
}

.btn-secondary:active {
	background: linear-gradient(135deg, #fcb69f 0%, #ffecd2 100%);
	transform: scale(0.98);
}

.btn-danger {
	background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
	color: #fff;
}

.btn-danger:active {
	background: linear-gradient(135deg, #ee5a24 0%, #ff6b6b 100%);
	transform: scale(0.98);
}

.form-textarea {
	width: 100%;
	min-height: 120rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 12rpx;
	padding: 20rpx 24rpx;
	font-size: 28rpx;
	color: #333;
	background-color: #fff;
	transition: all 0.3s ease;
	resize: vertical;
}

.form-textarea:focus {
	border-color: #007AFF;
	box-shadow: 0 0 0 4rpx rgba(0, 122, 255, 0.1);
}

/* 删除确认弹窗样式 */
.modal-header.danger {
	background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
	color: #fff;
}

.warning-content {
	text-align: center;
	padding: 40rpx 20rpx;
}

.warning-icon {
	font-size: 80rpx;
	margin-bottom: 20rpx;
}

.warning-text {
	font-size: 32rpx;
	color: #333;
	font-weight: 600;
	margin-bottom: 16rpx;
	display: block;
}

.warning-desc {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
	display: block;
}
</style>
<template>
	<view class="container">
		<view class="header">
			<text class="title">🔧 API接口测试工具</text>
			<text class="subtitle">帮助您测试和调试农机列表接口</text>
		</view>
		
		<view class="test-section">
			<view class="section-title">📡 接口配置</view>
			<view class="config-item">
				<text class="config-label">基础地址:</text>
				<input class="config-input" v-model="baseUrl" placeholder="输入API基础地址" />
			</view>
			<view class="config-item">
				<text class="config-label">接口路径:</text>
				<input class="config-input" v-model="apiPath" placeholder="输入接口路径" />
			</view>
			<view class="config-item">
				<text class="config-label">请求方式:</text>
				<picker @change="onMethodChange" :value="methodIndex" :range="methods">
					<view class="picker">{{ methods[methodIndex] }}</view>
				</picker>
			</view>
		</view>
		
		<view class="test-section">
			<view class="section-title">🔑 用户信息</view>
			<view class="user-info">
				<text class="info-text">用户ID: {{ userInfo.userId || '未登录' }}</text>
				<text class="info-text">用户名: {{ userInfo.username || '未登录' }}</text>
				<text class="info-text">角色: {{ userInfo.role || '未登录' }}</text>
				<text class="info-text">Token: {{ userInfo.token ? '已获取' : '未获取' }}</text>
			</view>
		</view>
		
		<view class="test-section">
			<view class="section-title">🚀 测试操作</view>
			<button class="test-btn" @click="testApi" :disabled="loading">
				{{ loading ? '测试中...' : '测试农机列表接口' }}
			</button>
			<button class="test-btn secondary" @click="testLoginApi" :disabled="loading">
				{{ loading ? '测试中...' : '测试登录接口' }}
			</button>
			<button class="test-btn secondary" @click="clearResults">清空结果</button>
		</view>
		
		<view class="test-section" v-if="testResults.length > 0">
			<view class="section-title">📊 测试结果</view>
			<view class="result-item" v-for="(result, index) in testResults" :key="index">
				<view class="result-header">
					<text class="result-time">{{ result.time }}</text>
					<text class="result-type" v-if="result.testType">{{ result.testType }}</text>
					<text class="result-status" :class="result.success ? 'success' : 'error'">
						{{ result.success ? '成功' : '失败' }}
					</text>
				</view>
				<view class="result-content">
					<text class="result-label">请求URL:</text>
					<text class="result-value">{{ result.url }}</text>
				</view>
				<view class="result-content">
					<text class="result-label">响应状态:</text>
					<text class="result-value">{{ result.statusCode }}</text>
				</view>
				<view class="result-content">
					<text class="result-label">响应数据:</text>
					<text class="result-data">{{ JSON.stringify(result.data, null, 2) }}</text>
				</view>
				<view class="result-content" v-if="result.error">
					<text class="result-label">错误信息:</text>
					<text class="result-error">{{ result.error }}</text>
				</view>
			</view>
		</view>
		
		<view class="help-section">
			<view class="section-title">💡 使用说明</view>
			<view class="help-item">
				<text class="help-text">1. 确保已经登录系统</text>
			</view>
			<view class="help-item">
				<text class="help-text">2. 农机列表接口使用路径参数格式</text>
			</view>
			<view class="help-item">
				<text class="help-text">3. URL格式: /fram/order/{用户手机号}</text>
			</view>
			<view class="help-item">
				<text class="help-text">4. 只需要在查询参数中传递token</text>
			</view>
			<view class="help-item">
				<text class="help-text">5. 点击"测试接口"查看结果</text>
			</view>
		</view>
	</view>
</template>

<script>
import { getUserInfo } from '@/common/utils/auth.js';

export default {
	data() {
		return {
			baseUrl: 'http://*************:8080',
			apiPath: '/fram/order/13800138000',
			methods: ['GET', 'POST'],
			methodIndex: 0,
			userInfo: {},
			loading: false,
			testResults: []
		}
	},
	onLoad() {
		this.userInfo = getUserInfo() || {};
	},
	methods: {
		onMethodChange(e) {
			this.methodIndex = e.detail.value;
		},
		
		async testApi() {
			if (!this.userInfo.token) {
				uni.showToast({
					title: '请先登录系统',
					icon: 'none'
				});
				return;
			}
			
			this.loading = true;
			const startTime = new Date();
			
			try {
				// 构建正确的URL，将用户名作为路径参数
				const url = this.baseUrl + '/fram/order/' + this.userInfo.username;
				const method = this.methods[this.methodIndex];
				
				console.log('开始测试接口:', {
					url: url,
					method: method,
					token: this.userInfo.token,
					orderSj: this.userInfo.username
				});
				
				const requestConfig = {
					url: url,
					method: method,
					header: {
						'Content-Type': 'application/json',
						'Accept': 'application/json',
						'Authorization': 'Bearer ' + this.userInfo.token
					},
					timeout: 10000
				};
				
				// 根据后端要求，只传递token作为查询参数
				if (method === 'POST') {
					requestConfig.data = {
						token: this.userInfo.token
					};
				} else {
					// GET请求，只传递token
					requestConfig.data = {
						token: this.userInfo.token
					};
				}
				
				const result = await this.makeRequest(requestConfig);
				
				this.testResults.unshift({
					time: startTime.toLocaleString(),
					url: url,
					method: method,
					success: true,
					statusCode: result.statusCode,
					data: result.data,
					duration: new Date() - startTime,
					testType: '农机列表接口'
				});
				
				uni.showToast({
					title: '接口测试成功',
					icon: 'success'
				});
				
			} catch (error) {
				console.error('接口测试失败:', error);
				
				this.testResults.unshift({
					time: startTime.toLocaleString(),
					url: this.baseUrl + '/fram/order/' + this.userInfo.username,
					method: this.methods[this.methodIndex],
					success: false,
					statusCode: error.statusCode || 0,
					data: error.data || null,
					error: error.message || '未知错误',
					duration: new Date() - startTime,
					testType: '农机列表接口'
				});
				
				uni.showToast({
					title: '接口测试失败',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},
		
		async testLoginApi() {
			this.loading = true;
			const startTime = new Date();
			
			try {
				const url = 'http://*************:8080/login';
				const method = 'POST';
				
				// 测试数据
				const testData = {
					code: 2,
					username: '13800138000',
					password: '123456'
				};
				
				console.log('开始测试登录接口:', {
					url: url,
					method: method,
					data: testData
				});
				
				const requestConfig = {
					url: url,
					method: method,
					header: {
						'Content-Type': 'application/json',
						'Accept': 'application/json'
					},
					data: testData,
					timeout: 10000
				};
				
				const result = await this.makeRequest(requestConfig);
				
				this.testResults.unshift({
					time: startTime.toLocaleString(),
					url: url,
					method: method,
					success: true,
					statusCode: result.statusCode,
					data: result.data,
					duration: new Date() - startTime,
					testType: '登录接口'
				});
				
				uni.showToast({
					title: '登录接口测试成功',
					icon: 'success'
				});
				
			} catch (error) {
				console.error('登录接口测试失败:', error);
				
				this.testResults.unshift({
					time: startTime.toLocaleString(),
					url: 'http://192.168.1.105:8080/login',
					method: 'POST',
					success: false,
					statusCode: error.statusCode || 0,
					data: error.data || null,
					error: error.message || '未知错误',
					duration: new Date() - startTime,
					testType: '登录接口'
				});
				
				uni.showToast({
					title: '登录接口测试失败',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},
		
		makeRequest(config) {
			return new Promise((resolve, reject) => {
				uni.request({
					...config,
					success: (res) => {
						console.log('接口响应:', res);
						resolve(res);
					},
					fail: (err) => {
						console.error('接口错误:', err);
						reject({
							message: err.errMsg || '网络请求失败',
							statusCode: 0,
							data: null
						});
					}
				});
			});
		},
		
		clearResults() {
			this.testResults = [];
			uni.showToast({
				title: '已清空测试结果',
				icon: 'success'
			});
		}
	}
}
</script>

<style>
.container {
	padding: 20rpx;
	background-color: #f8f8f8;
	min-height: 100vh;
}

.header {
	text-align: center;
	margin-bottom: 40rpx;
}

.title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 10rpx;
}

.subtitle {
	font-size: 26rpx;
	color: #666;
	display: block;
}

.test-section {
	background-color: #fff;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
}

.config-item {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}

.config-label {
	font-size: 28rpx;
	color: #666;
	width: 160rpx;
	flex-shrink: 0;
}

.config-input {
	flex: 1;
	height: 70rpx;
	padding: 0 20rpx;
	background-color: #f5f5f5;
	border-radius: 8rpx;
	font-size: 26rpx;
}

.picker {
	flex: 1;
	height: 70rpx;
	line-height: 70rpx;
	padding: 0 20rpx;
	background-color: #f5f5f5;
	border-radius: 8rpx;
	font-size: 26rpx;
}

.user-info {
	display: flex;
	flex-direction: column;
	gap: 10rpx;
}

.info-text {
	font-size: 26rpx;
	color: #333;
	padding: 10rpx 0;
}

.test-btn {
	width: 100%;
	height: 80rpx;
	line-height: 80rpx;
	background: linear-gradient(135deg, #007AFF, #00c6ff);
	color: #fff;
	border-radius: 40rpx;
	font-size: 30rpx;
	font-weight: bold;
	margin-bottom: 20rpx;
	border: none;
}

.test-btn.secondary {
	background: linear-gradient(135deg, #666, #999);
}

.test-btn:disabled {
	opacity: 0.6;
}

.result-item {
	background-color: #f9f9f9;
	border-radius: 12rpx;
	padding: 20rpx;
	margin-bottom: 20rpx;
}

.result-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 15rpx;
}

.result-time {
	font-size: 24rpx;
	color: #666;
}

.result-type {
	font-size: 24rpx;
	font-weight: bold;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	background-color: #007AFF;
	color: #fff;
	margin: 0 10rpx;
}

.result-status {
	font-size: 24rpx;
	font-weight: bold;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
}

.result-status.success {
	background-color: #4cd964;
	color: #fff;
}

.result-status.error {
	background-color: #ff3b30;
	color: #fff;
}

.result-content {
	margin-bottom: 10rpx;
}

.result-label {
	font-size: 24rpx;
	color: #666;
	display: block;
	margin-bottom: 5rpx;
}

.result-value {
	font-size: 26rpx;
	color: #333;
	display: block;
}

.result-data {
	font-size: 22rpx;
	color: #333;
	background-color: #fff;
	padding: 10rpx;
	border-radius: 8rpx;
	font-family: 'Courier New', monospace;
	white-space: pre-wrap;
	word-break: break-all;
	display: block;
}

.result-error {
	font-size: 24rpx;
	color: #ff3b30;
	display: block;
}

.help-section {
	background-color: #fff;
	border-radius: 16rpx;
	padding: 30rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.help-item {
	margin-bottom: 15rpx;
}

.help-text {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
}
</style> 
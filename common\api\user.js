// 导入模拟数据（实际项目中这里会改为真实API请求）
import { mockLogin, mockGetUserInfo, mockRegister } from '../mock/user.js';

// 登录API
export const login = (username, password, role) => {
	return new Promise((resolve, reject) => {
		// 根据角色类型转换为数字代码（1表示管理员，2表示用户）
		const codeValue = role === 'admin' ? 1 : 2;
		
		// 尝试不同的数据格式，根据后端要求调整
		// 格式1: 当前格式
		const requestData = {
			code: codeValue,
			username: username,
			password: password
		};
		
		// 格式2: 字符串code (如果后端期望字符串)
		// const requestData = {
		// 	code: String(codeValue),
		// 	username: username,
		// 	password: password
		// };
		
		// 格式3: 不同的字段名 (如果后端使用不同命名)
		// const requestData = {
		// 	userName: username,
		// 	passWord: password,
		// 	roleCode: codeValue
		// };
		
		// 格式4: 简化格式 (只有用户名密码)
		// const requestData = {
		// 	username: username,
		// 	password: password
		// };
		
		// 在发送请求前打印一下要发送的数据，方便调试
		console.log('Login API params:', requestData);
		console.log('Login API JSON:', JSON.stringify(requestData));
		
		uni.request({
			url: 'http://192.168.1.106:8080/login',
			method: 'POST',
			header: {
				'Content-Type': 'application/json',
				'Accept': 'application/json'
			},
			// 确保数据格式正确
			data: requestData,
			success: (res) => {
				// 打印服务器响应，方便调试
				console.log('Login API response:', res);
				
				// 检查响应状态码和数据
				if (res.statusCode === 200) {
					// 获取响应数据
					const responseData = res.data;
					
					// 如果返回的数据没有code字段，添加一个，用于区分失败和成功
					if (responseData && typeof responseData.code === 'undefined') {
						responseData.code = res.statusCode;
					}
					
					// 检查是否是明确的错误信息
					if (responseData && responseData.msg && 
						(responseData.msg.includes('密码错误') || 
						 responseData.msg.includes('不存在') || 
						 responseData.msg.includes('失败'))) {
						// 这是一个业务层面的错误，响应码是200但登录失败
						reject({ message: responseData.msg });
						return;
					}
					
					// 保存roleType，确保前端可以使用
					if (responseData && !responseData.roleType) {
						responseData.roleType = role;
					}
					
					resolve(responseData);
				} else {
					// HTTP错误
					const errorMsg = res.data && (res.data.message || res.data.msg) ? 
						(res.data.message || res.data.msg) : '登录失败';
					reject({ message: errorMsg });
				}
			},
			fail: (err) => {
				console.error('Login API error:', err);
				reject({ message: '网络错误，请稍后再试' });
			}
		});
	});
}
// 获取用户信息API
export const getUserInfo = (token) => {
	return new Promise((resolve, reject) => {
		// 在发送请求前打印一下要发送的数据，方便调试
		console.log('GetUserInfo API params, with token:', token);
		
		uni.request({
			url: 'http://192.168.1.106:8080/getUserInfo',
			method: 'GET',
			header: {
				'Content-Type': 'application/json',
				'Accept': 'application/json',
				'Authorization': 'Bearer ' + token // 在请求头中添加token
			},
			success: (res) => {
				// 打印服务器响应，方便调试
				console.log('GetUserInfo API response:', res);
				
				// 处理成功响应
				if (res.statusCode === 200) {
					resolve({
						code: 200,
						message: '获取用户信息成功',
						data: res.data
					});
				} else {
					reject({ 
						code: res.statusCode, 
						message: res.data.message || res.data.msg || '获取用户信息失败' 
					});
				}
			},
			fail: (err) => {
				console.error('GetUserInfo API error:', err);
				reject({ 
					code: 500,
					message: '网络错误，请稍后再试' 
				});
			}
		});
	});
};

// 用户注册API
export function register(username, password, roleType = 'user') {
	return new Promise((resolve, reject) => {
		console.log('Register API params:', {
			userName: username,
			password: password,
			code: roleType,
			userId: 'id1',
			phonenumber: '18888888888'
		});
		
		uni.request({
			url: 'http://192.168.1.106:8080/register',
			method: 'POST',
			header: {
				'Content-Type': 'application/json',
				'Accept': 'application/json'
			},
			data: {
				userName: username,
				password: password,
				code: roleType,
				userId: 'id1',
				phonenumber: '18888888888'
			},
			success: (res) => {
				console.log('Register API response:', res);
				
				if (res.statusCode === 200) {
					resolve(res.data);
				} else {
					reject({ message: res.data.message || res.data.msg || '注册失败' });
				}
			},
			fail: (err) => {
				console.error('Register API error:', err);
				reject({ message: '网络错误，请稍后再试' });
			}
		});
	});
}

// 检查登录状态
export const checkLoginStatus = () => {
	return new Promise(async (resolve, reject) => {
		try {
			// 从本地存储获取用户信息
			const userInfo = uni.getStorageSync('userInfo');
			
			if (!userInfo || !userInfo.token) {
				reject({
					code: 401,
					message: '未登录'
				});
				return;
			}
			
			// 使用token检查登录状态
			try {
				const result = await getUserInfo(userInfo.token);
				
				if (result.code === 200) {
					resolve(result.data);
				} else {
					// 清除失效的登录信息
					uni.removeStorageSync('userInfo');
					reject({
						code: result.code || 401,
						message: result.message || '登录已过期'
					});
				}
			} catch (apiError) {
				// API调用失败，可能是token无效或网络问题
				uni.removeStorageSync('userInfo');
				reject({
					code: apiError.code || 401,
					message: apiError.message || '登录已过期'
				});
			}
		} catch (err) {
			// 清除失效的登录信息
			uni.removeStorageSync('userInfo');
			reject({
				code: 500,
				message: err.message || '登录状态检查失败'
			});
		}
	});
};

// 退出登录
export const logout = () => {
	return new Promise((resolve, reject) => {
		try {
			// 获取存储的用户信息和token
			const userInfo = uni.getStorageSync('userInfo');
			const token = userInfo && userInfo.token ? userInfo.token : '';
			
			// 向后端发送登出请求
			if (token) {
				uni.request({
					url: 'http://192.168.1.106:8080/logout',
					method: 'POST',
					header: {
						'Content-Type': 'application/json',
						'Authorization': 'Bearer ' + token
					},
					success: (res) => {
						console.log('Logout API response:', res);
						// 无论后端返回什么，我们都清除本地存储
					},
					fail: (err) => {
						console.error('Logout API error:', err);
						// 即使请求失败，我们也清除本地存储
					},
					complete: () => {
						// 清除本地存储的用户信息
						uni.removeStorageSync('userInfo');
						
						// 返回成功
						resolve({
							code: 200,
							message: '退出登录成功'
						});
					}
				});
			} else {
				// 没有token也直接清除本地存储
				uni.removeStorageSync('userInfo');
				resolve({
					code: 200,
					message: '退出登录成功'
				});
			}
		} catch (err) {
			// 出错时也清除本地存储
			uni.removeStorageSync('userInfo');
			console.error('Logout error:', err);
			resolve({
				code: 200,
				message: '退出登录成功'
			});
		}
	});
}; 
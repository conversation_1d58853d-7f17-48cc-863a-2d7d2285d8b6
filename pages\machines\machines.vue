<template>
	<view class="container">
		<view class="header">
			<view class="search-box">
				<input type="text" v-model="searchKey" placeholder="搜索农机名称、作业名称或司机" class="search-input" />
				<text class="search-icon">🔍</text>
			</view>
			<view class="filter-tabs">
				<view 
					class="filter-tab" 
					:class="{ 'active': currentFilter === 'all' }" 
					@click="setFilter('all')"
				>
					全部
				</view>
				<view 
					class="filter-tab" 
					:class="{ 'active': currentFilter === 'working' }" 
					@click="setFilter('working')"
				>
					作业中
				</view>
				<view 
					class="filter-tab" 
					:class="{ 'active': currentFilter === 'idle' }" 
					@click="setFilter('idle')"
				>
					空闲
				</view>
				<view 
					class="filter-tab" 
					:class="{ 'active': currentFilter === 'maintenance' }" 
					@click="setFilter('maintenance')"
				>
					维护中
				</view>
			</view>
			
			<!-- 美化的刷新按钮 -->
			<view class="refresh-section">
				<view class="refresh-btn" @click="refreshData" :class="{ 'refreshing': loading }">
					<text class="refresh-icon">🔄</text>
					<text class="refresh-text">{{ loading ? '刷新中...' : '刷新数据' }}</text>
				</view>
			</view>
		</view>
		
		<view class="machine-list" v-if="filteredMachines.length > 0">
			<view
				class="machine-item"
				v-for="(machine, index) in filteredMachines"
				:key="machine.id"
				@click="goToDetail(machine.id)"
				:style="{ 'animation-delay': (index * 0.1) + 's' }"
			>
				<!-- 状态指示器 -->
				<view class="status-indicator">
					<view class="status-dot" :class="machine.status"></view>
					<view class="status-line" :class="machine.status"></view>
				</view>

				<!-- 主要内容区域 -->
				<view class="machine-content">
					<!-- 头部信息 -->
					<view class="machine-header">
						<view class="header-left">
							<text class="machine-name">{{ getMachineDisplayName(machine) }}</text>
							<text class="machine-model">{{ getMachineModel(machine) }}</text>
						</view>
						<view class="status-badge" :class="machine.status">
							<text class="status-text">{{ getStatusText(machine.status) }}</text>
						</view>
					</view>

					<!-- 核心数据展示 -->
					<view class="data-showcase">
						<view class="data-item primary">
							<view class="data-icon working">🌾</view>
							<view class="data-content">
								<text class="data-value">{{ machine.workArea || 0 }}</text>
								<text class="data-unit">亩</text>
								<text class="data-label">作业面积</text>
							</view>
						</view>
						<view class="data-item secondary">
							<view class="data-icon packages">📦</view>
							<view class="data-content">
								<text class="data-value">{{ machine.packageCount || 0 }}</text>
								<text class="data-unit">个</text>
								<text class="data-label">打捆数量</text>
							</view>
						</view>
					</view>

					<!-- 详细信息 -->
					<view class="machine-details">
						<view class="detail-row">
							<view class="detail-item">
								<view class="detail-icon">👨‍💼</view>
								<view class="detail-content">
									<text class="detail-label">操作员</text>
									<text class="detail-value">{{ getMachineOwner(machine) }}</text>
								</view>
							</view>
							<view class="detail-item">
								<view class="detail-icon">📱</view>
								<view class="detail-content">
									<text class="detail-label">联系方式</text>
									<text class="detail-value">{{ getMachinePhone(machine) }}</text>
								</view>
							</view>
						</view>

						<view class="detail-row">
							<view class="detail-item full-width">
								<view class="detail-icon">📍</view>
								<view class="detail-content">
									<text class="detail-label">作业地点</text>
									<text class="detail-value location">{{ getLocationText(machine.originalOrder) }}</text>
								</view>
							</view>
						</view>

						<view class="detail-row" v-if="getMachineMemo(machine)">
							<view class="detail-item full-width">
								<view class="detail-icon">📝</view>
								<view class="detail-content">
									<text class="detail-label">备注信息</text>
									<text class="detail-value memo">{{ getMachineMemo(machine) }}</text>
								</view>
							</view>
						</view>
					</view>

					<!-- 底部时间信息 -->
					<view class="machine-footer">
						<view class="time-info">
							<text class="time-label">最后更新</text>
							<text class="time-value">{{ formatTime(machine.lastActiveTime) }}</text>
						</view>
						<view class="arrow-icon">
							<text class="arrow">›</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<view class="empty-tip" v-else-if="!loading">
			<view class="empty-icon">📋</view>
			<text class="empty-text">{{ getEmptyText() }}</text>
			<text class="empty-desc">{{ getEmptyDesc() }}</text>
		</view>
		
		<view class="loading-tip" v-if="loading">
			<text class="loading-text">加载中...</text>
		</view>

		<!-- 浮动注册按钮 -->
		<view class="floating-add-btn" @click="showRegisterModal">
			<text class="add-icon">+</text>
		</view>

		<!-- 农机注册弹窗 -->
		<view class="modal-overlay" v-if="showRegisterForm" @click="hideRegisterModal">
			<view class="modal-content" @click.stop>
				<view class="modal-header">
					<text class="modal-title">注册新农机</text>
					<view class="modal-close" @click="hideRegisterModal">
						<text>×</text>
					</view>
				</view>

				<view class="modal-body">
					<view class="form-group">
						<text class="form-label">农机名称 *</text>
						<input
							class="form-input"
							v-model="registerForm.fname"
							placeholder="请输入农机名称"
							maxlength="50"
						/>
					</view>

					<view class="form-group">
						<text class="form-label">规格型号 *</text>
						<input
							class="form-input"
							v-model="registerForm.guige"
							placeholder="请输入规格型号"
							maxlength="30"
						/>
					</view>

					<view class="form-group">
						<text class="form-label">车主姓名 *</text>
						<input
							class="form-input"
							v-model="registerForm.owner"
							placeholder="请输入车主姓名"
							maxlength="20"
						/>
					</view>

					<view class="form-group">
						<text class="form-label">生产厂家 *</text>
						<input
							class="form-input"
							v-model="registerForm.company"
							placeholder="请输入生产厂家"
							maxlength="50"
						/>
					</view>

					<view class="form-group">
						<text class="form-label">品牌 *</text>
						<input
							class="form-input"
							v-model="registerForm.brand"
							placeholder="请输入品牌"
							maxlength="30"
						/>
					</view>

					<view class="form-group">
						<text class="form-label">联系电话 *</text>
						<input
							class="form-input"
							v-model="registerForm.mobile"
							placeholder="请输入联系电话"
							type="number"
							maxlength="11"
						/>
					</view>

					<view class="form-group">
						<text class="form-label">备注</text>
						<textarea
							class="form-textarea"
							v-model="registerForm.memo"
							placeholder="请输入备注信息（可选）"
							maxlength="200"
						/>
					</view>
				</view>

				<view class="modal-footer">
					<view class="btn btn-cancel" @click="hideRegisterModal">
						<text>取消</text>
					</view>
					<view class="btn btn-primary" @click="submitRegister" :class="{ 'btn-loading': registerLoading }">
						<text v-if="!registerLoading">确认注册</text>
						<text v-else>注册中...</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { getUserInfo } from '@/common/utils/auth.js';
import { getMachineList, getMachineListNew, registerMachine } from '@/common/api/machines.js';

export default {
	data() {
		return {
			userInfo: null,
			machines: [],
			searchKey: '',
			currentFilter: 'all', // 'all', 'working', 'idle', 'maintenance'
			loading: false,
			// 注册相关数据
			showRegisterForm: false,
			registerLoading: false,
			registerForm: {
				fname: '',
				guige: '',
				owner: '',
				company: '',
				brand: '',
				mobile: '',
				memo: ''
			}
		}
	},
	computed: {
		filteredMachines() {
			let filtered = this.machines;
			
			// 按状态过滤
			if (this.currentFilter !== 'all') {
				filtered = filtered.filter(machine => machine.status === this.currentFilter);
			}
			
			// 按搜索关键词过滤
			if (this.searchKey) {
				const key = this.searchKey.toLowerCase();
				filtered = filtered.filter(machine => 
					machine.name.toLowerCase().includes(key) || 
					machine.model.toLowerCase().includes(key) ||
					machine.driver.name.toLowerCase().includes(key) ||
					(machine.originalOrder && machine.originalOrder.orderWhere && 
					 machine.originalOrder.orderWhere.toLowerCase().includes(key))
				);
			}
			
			return filtered;
		}
	},
	onLoad() {
		this.userInfo = getUserInfo();
		this.loadMachines();
	},
	onShow() {
		// 每次页面显示时重新获取数据
		this.loadMachines();
	},
	onPullDownRefresh() {
		// 下拉刷新
		this.loadMachines();
	},
	onReachBottom() {
		// 上拉刷新
		this.refreshData();
	},
	methods: {
		async loadMachines() {
			if (!this.userInfo) {
				return;
			}
			
			this.loading = true;
			
			try {
				// 使用新的农机列表接口
				const result = await getMachineListNew(this.userInfo);
				if (result.code === 200) {
					this.machines = result.data || [];
					console.log('🚜 农机列表加载成功，共', this.machines.length, '台农机');
				} else {
					// 检查是否是认证失败
					if (result.code === 401) {
						uni.showModal({
							title: '认证失败',
							content: '登录已过期，请重新登录',
							showCancel: false,
							success: () => {
								// 清除用户信息并跳转到登录页
								uni.removeStorageSync('userInfo');
								uni.reLaunch({
									url: '/pages/login/login'
								});
							}
						});
						return;
					}
					
					// 检查是否是后端路径冲突错误
					if (result.code === 500 && result.msg && result.msg.includes('Ambiguous handler methods')) {
						uni.showModal({
							title: '后端接口错误',
							content: '后端接口路径冲突，请联系后端开发者修复',
							showCancel: false
						});
						return;
					}
					
					uni.showToast({
						title: result.message || result.msg || '获取数据失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('获取农机列表失败:', error);
				
				// 检查是否是认证失败
				if (error.code === 401) {
					uni.showModal({
						title: '认证失败',
						content: '登录已过期，请重新登录',
						showCancel: false,
						success: () => {
							// 清除用户信息并跳转到登录页
							uni.removeStorageSync('userInfo');
							uni.reLaunch({
								url: '/pages/login/login'
							});
						}
					});
					return;
				}
				
				// 检查是否是后端路径冲突错误
				if (error.message && error.message.includes('Ambiguous handler methods')) {
					uni.showModal({
						title: '后端接口错误',
						content: '后端接口路径冲突，请联系后端开发者修复',
						showCancel: false
					});
					return;
				}
				
				uni.showToast({
					title: '获取农机列表失败',
					icon: 'none'
				});
			} finally {
				this.loading = false;
				uni.stopPullDownRefresh();
			}
		},
		
		// 刷新数据
		async refreshData() {
			if (this.loading) return; // 防止重复请求
			
			uni.showToast({
				title: '正在刷新...',
				icon: 'loading',
				duration: 1000
			});
			
			// 重新加载数据
			await this.loadMachines();
			
			uni.showToast({
				title: '刷新完成',
				icon: 'success',
				duration: 1000
			});
		},
		
		setFilter(filter) {
			this.currentFilter = filter;
		},
		
		goToDetail(machineId) {
			try {
				// 找到对应的农机数据
				const machine = this.machines.find(m => m.id == machineId);
				if (machine) {
					// 将农机数据存储到全局，供详情页面使用
					getApp().globalData.currentMachine = machine;
					console.log('🚜 跳转到农机详情，传递数据:', machine);
					
					uni.navigateTo({
						url: `/pages/machine-detail/machine-detail?id=${machineId}`
					});
				} else {
					console.warn('⚠️ 未找到对应的农机数据, machineId:', machineId);
					uni.showToast({
						title: '农机数据不存在',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('❌ 跳转到农机详情时发生错误:', error);
				uni.showToast({
					title: '跳转失败，请重试',
					icon: 'none'
				});
			}
		},
		
		getStatusText(status) {
			const statusMap = {
				'working': '作业中',
				'idle': '空闲',
				'maintenance': '维护中'
			};
			
			return statusMap[status] || '未知';
		},
		
		getLocationText(order) {
			if (!order || !order.orderWhere) return '未知地点';
			
			// 如果地点名称太长，进行截取
			const location = order.orderWhere;
			if (location.length > 12) {
				return location.substring(0, 12) + '...';
			}
			return location;
		},
		
		formatTime(timeStr) {
			if (!timeStr) return '未知';
			
			// 如果是日期格式，直接返回
			if (timeStr.includes('-') && !timeStr.includes(' ')) {
				return timeStr;
			}
			
			// 简单格式化，实际项目中可能需要更复杂的处理
			try {
				const date = new Date(timeStr.replace(/-/g, '/'));
				const today = new Date();
				today.setHours(0, 0, 0, 0);
				
				if (date >= today) {
					// 今天的记录只显示时间
					return timeStr.split(' ')[1] || timeStr;
				} else {
					// 非今天的记录显示日期
					return timeStr.split(' ')[0] || timeStr;
				}
			} catch (error) {
				return timeStr;
			}
		},
		
		getEmptyText() {
			if (this.currentFilter === 'all') {
				return this.searchKey ? '未找到相关农机' : '暂无农机数据';
			} else {
				const statusText = this.getStatusText(this.currentFilter);
				return `暂无${statusText}的农机`;
			}
		},
		
		getEmptyDesc() {
			if (this.searchKey) {
				return '请尝试其他搜索关键词';
			} else if (this.currentFilter !== 'all') {
				return '可以切换到其他状态查看';
			} else {
				return '请联系管理员分配农机权限';
			}
		},

		// 获取农机显示名称
		getMachineDisplayName(machine) {
			return machine.fname || machine.name || machine.orderNjname || '未命名农机';
		},

		// 获取农机型号
		getMachineModel(machine) {
			return machine.guige || machine.model || '未知型号';
		},

		// 获取农机车主/操作员
		getMachineOwner(machine) {
			return machine.owner || (machine.driver && machine.driver.name) || machine.orderSj || '未知';
		},

		// 获取联系电话
		getMachinePhone(machine) {
			if (machine.mobile) {
				// 手机号脱敏显示
				return machine.mobile.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
			}
			if (machine.driver && machine.driver.phone) {
				return machine.driver.phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
			}
			return '未知';
		},

		// 获取备注信息
		getMachineMemo(machine) {
			return machine.memo || (machine.originalOrder && machine.originalOrder.orderMemo) || '';
		},

		// 显示注册弹窗
		showRegisterModal() {
			this.showRegisterForm = true;
			this.resetRegisterForm();
		},

		// 隐藏注册弹窗
		hideRegisterModal() {
			this.showRegisterForm = false;
			this.resetRegisterForm();
		},

		// 重置注册表单
		resetRegisterForm() {
			this.registerForm = {
				fname: '',
				guige: '',
				owner: '',
				company: '',
				brand: '',
				mobile: '',
				memo: ''
			};
			this.registerLoading = false;
		},

		// 验证注册表单
		validateRegisterForm() {
			const { fname, guige, owner, company, brand, mobile } = this.registerForm;

			if (!fname.trim()) {
				uni.showToast({
					title: '请输入农机名称',
					icon: 'none'
				});
				return false;
			}

			if (!guige.trim()) {
				uni.showToast({
					title: '请输入规格型号',
					icon: 'none'
				});
				return false;
			}

			if (!owner.trim()) {
				uni.showToast({
					title: '请输入车主姓名',
					icon: 'none'
				});
				return false;
			}

			if (!company.trim()) {
				uni.showToast({
					title: '请输入生产厂家',
					icon: 'none'
				});
				return false;
			}

			if (!brand.trim()) {
				uni.showToast({
					title: '请输入品牌',
					icon: 'none'
				});
				return false;
			}

			if (!mobile.trim()) {
				uni.showToast({
					title: '请输入联系电话',
					icon: 'none'
				});
				return false;
			}

			// 验证手机号格式
			const mobileRegex = /^1[3-9]\d{9}$/;
			if (!mobileRegex.test(mobile.trim())) {
				uni.showToast({
					title: '请输入正确的手机号',
					icon: 'none'
				});
				return false;
			}

			return true;
		},

		// 提交注册 - 真实后端联调版本
		async submitRegister() {
			if (!this.validateRegisterForm()) {
				return;
			}

			this.registerLoading = true;

			try {
				console.log('🚜 开始注册农机，请求参数:', {
					userInfo: {
						userId: this.userInfo.userId,
						username: this.userInfo.username,
						token: this.userInfo.token ? '***有token***' : '无token'
					},
					registerForm: this.registerForm
				});

				// 调用真实的后端API
				const result = await registerMachine(this.userInfo, this.registerForm);
				
				console.log('🚜 注册农机API响应:', result);

								if (result.code === 200) {
					// 注册成功 - 先关闭弹窗
					console.log('✅ 农机注册成功:', result);
					this.hideRegisterModal();
					
					// 然后显示成功弹窗
					uni.showModal({
						title: '✅ 注册成功',
						content: `农机 "${this.registerForm.fname}" 已成功注册到系统中！`,
						showCancel: false,
						confirmText: '确定',
						success: () => {
							// 显示成功提示
							uni.showToast({
								title: '农机注册成功',
								icon: 'success',
								duration: 2000
							});

							// 立即刷新农机列表
							this.loadMachines();
						}
					});
				} else {
					// 注册失败 - 显示具体错误信息
					console.error('❌ 注册失败:', result);
					
					let errorTitle = '注册失败';
					let errorContent = result.message || '未知错误';
					
					// 根据错误代码提供更友好的提示
					if (result.code === 400) {
						errorTitle = '⚠️ 参数错误';
						if (result.message.includes('缺少必需字段')) {
							errorContent = '请检查所有必填项是否填写完整';
						} else if (result.message.includes('已存在')) {
							errorContent = '该农机名称或编号已存在，请使用其他名称';
						} else if (result.message.includes('手机号')) {
							errorContent = '该手机号已被其他农机绑定，请使用其他手机号';
						}
					} else if (result.code === 401) {
						errorTitle = '🔐 认证失败';
						errorContent = '登录已过期，请重新登录后再试';
					} else if (result.code === 500) {
						errorTitle = '🔧 服务器错误';
						errorContent = '服务器暂时无法处理请求，请稍后重试';
					}

					uni.showModal({
						title: errorTitle,
						content: `${errorContent}\n\n详细信息: ${result.message}`,
						showCancel: true,
						cancelText: '取消',
						confirmText: '重试',
						success: (res) => {
							if (res.confirm) {
								// 用户选择重试
								setTimeout(() => {
									this.submitRegister();
								}, 500);
							}
						}
					});
				}
			} catch (error) {
				console.error('🚨 注册农机网络错误:', error);
				
				// 网络错误处理
				uni.showModal({
					title: '🌐 网络错误',
					content: `网络连接失败，请检查网络设置后重试\n\n错误详情: ${error.message || '未知网络错误'}`,
					showCancel: true,
					cancelText: '取消',
					confirmText: '重试',
					success: (res) => {
						if (res.confirm) {
							setTimeout(() => {
								this.submitRegister();
							}, 500);
						}
					}
				});
			} finally {
				this.registerLoading = false;
			}
		}
	}
}
</script>

<style>
.container {
	background-color: #f8f8f8;
	min-height: 100vh;
}

.header {
	padding: 20rpx;
	background-color: #fff;
	position: sticky;
	top: 0;
	z-index: 10;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.search-box {
	position: relative;
	margin-bottom: 20rpx;
}

.search-input {
	width: 100%;
	height: 70rpx;
	background-color: #f5f5f5;
	border-radius: 35rpx;
	padding: 0 70rpx 0 30rpx;
	font-size: 28rpx;
	border: 2rpx solid transparent;
	transition: all 0.3s ease;
}

.search-input:focus {
	background-color: #fff;
	border-color: #007AFF;
	box-shadow: 0 0 0 4rpx rgba(0, 122, 255, 0.1);
}

.search-icon {
	position: absolute;
	right: 30rpx;
	top: 50%;
	transform: translateY(-50%);
	font-size: 32rpx;
	color: #999;
}

.filter-tabs {
	display: flex;
	gap: 20rpx;
}

.filter-tab {
	flex: 1;
	height: 60rpx;
	line-height: 60rpx;
	text-align: center;
	background-color: #f5f5f5;
	border-radius: 30rpx;
	font-size: 26rpx;
	color: #666;
	transition: all 0.3s ease;
}

.filter-tab.active {
	background-color: #007AFF;
	color: #fff;
	transform: scale(1.02);
}

/* 美化的刷新按钮 */
.refresh-section {
	display: flex;
	justify-content: center;
	margin-top: 20rpx;
}

.refresh-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 10rpx;
	padding: 12rpx 20rpx;
	background-color: #f5f5f5;
	border-radius: 30rpx;
	font-size: 26rpx;
	color: #666;
	transition: all 0.3s ease;
}

.refresh-btn:active {
	transform: scale(0.98);
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.12);
}

.refresh-icon {
	font-size: 28rpx;
}

.refresh-text {
	font-size: 26rpx;
	font-weight: 500;
}

.refreshing .refresh-text {
	color: #007AFF;
}

/* 农机列表样式 */
.machine-list {
	padding: 20rpx;
}

.machine-item {
	background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
	border-radius: 24rpx;
	margin-bottom: 24rpx;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
	position: relative;
	overflow: hidden;
	transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	animation: slideInUp 0.6s ease-out both;
	border: 1rpx solid rgba(0, 0, 0, 0.04);
}

@keyframes slideInUp {
	from {
		opacity: 0;
		transform: translateY(60rpx);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

.machine-item:active {
	transform: translateY(-4rpx) scale(0.98);
	box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.12);
}

/* 状态指示器 */
.status-indicator {
	position: absolute;
	left: 0;
	top: 0;
	bottom: 0;
	width: 8rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.status-dot {
	width: 16rpx;
	height: 16rpx;
	border-radius: 50%;
	margin-top: 30rpx;
	z-index: 2;
	box-shadow: 0 0 0 4rpx #fff;
}

.status-line {
	flex: 1;
	width: 4rpx;
	margin-top: 8rpx;
	opacity: 0.3;
}

.status-dot.working, .status-line.working {
	background: linear-gradient(135deg, #34C759, #28A745);
}

.status-dot.idle, .status-line.idle {
	background: linear-gradient(135deg, #8E8E93, #6D6D70);
}

.status-dot.maintenance, .status-line.maintenance {
	background: linear-gradient(135deg, #FF9500, #CC7700);
}

/* 主要内容区域 */
.machine-content {
	padding: 30rpx 30rpx 30rpx 50rpx;
}

/* 头部信息 */
.machine-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 24rpx;
}

.header-left {
	flex: 1;
}

.machine-name {
	font-size: 36rpx;
	font-weight: 700;
	color: #1a1a1a;
	line-height: 1.2;
	margin-bottom: 8rpx;
	letter-spacing: -0.5rpx;
}

.machine-model {
	font-size: 26rpx;
	color: #666;
	font-weight: 500;
	background: rgba(0, 122, 255, 0.08);
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	display: inline-block;
}

.status-badge {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 22rpx;
	font-weight: 600;
	color: #fff;
	text-align: center;
	min-width: 80rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.status-badge.working {
	background: linear-gradient(135deg, #34C759, #28A745);
}

.status-badge.idle {
	background: linear-gradient(135deg, #8E8E93, #6D6D70);
}

.status-badge.maintenance {
	background: linear-gradient(135deg, #FF9500, #CC7700);
}

/* 核心数据展示 */
.data-showcase {
	display: flex;
	gap: 24rpx;
	margin-bottom: 32rpx;
	padding: 24rpx;
	background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
	border-radius: 20rpx;
	border: 1rpx solid rgba(0, 122, 255, 0.1);
}

.data-item {
	flex: 1;
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.data-icon {
	width: 60rpx;
	height: 60rpx;
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 28rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.data-icon.working {
	background: linear-gradient(135deg, #34C759, #28A745);
}

.data-icon.packages {
	background: linear-gradient(135deg, #007AFF, #0056CC);
}

.data-content {
	flex: 1;
}

.data-value {
	font-size: 32rpx;
	font-weight: 700;
	color: #1a1a1a;
	line-height: 1;
}

.data-unit {
	font-size: 20rpx;
	color: #666;
	margin-left: 4rpx;
}

.data-label {
	font-size: 22rpx;
	color: #666;
	margin-top: 4rpx;
	display: block;
}

/* 详细信息 */
.machine-details {
	margin-bottom: 24rpx;
}

.detail-row {
	display: flex;
	gap: 24rpx;
	margin-bottom: 20rpx;
}

.detail-row:last-child {
	margin-bottom: 0;
}

.detail-item {
	flex: 1;
	display: flex;
	align-items: flex-start;
	gap: 12rpx;
}

.detail-item.full-width {
	flex: none;
	width: 100%;
}

.detail-icon {
	width: 40rpx;
	height: 40rpx;
	border-radius: 12rpx;
	background: rgba(0, 122, 255, 0.1);
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 20rpx;
	flex-shrink: 0;
	margin-top: 2rpx;
}

.detail-content {
	flex: 1;
	min-width: 0;
}

.detail-label {
	font-size: 22rpx;
	color: #666;
	margin-bottom: 4rpx;
	display: block;
}

.detail-value {
	font-size: 26rpx;
	color: #333;
	font-weight: 500;
	word-wrap: break-word;
}

.detail-value.location {
	color: #007AFF;
}

.detail-value.memo {
	color: #666;
	line-height: 1.4;
	font-weight: 400;
}

/* 底部信息 */
.machine-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding-top: 20rpx;
	border-top: 1rpx solid rgba(0, 0, 0, 0.06);
}

.time-info {
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.time-label {
	font-size: 22rpx;
	color: #999;
}

.time-value {
	font-size: 24rpx;
	color: #666;
	font-weight: 500;
}

.arrow-icon {
	width: 40rpx;
	height: 40rpx;
	border-radius: 20rpx;
	background: rgba(0, 122, 255, 0.1);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
}

.arrow {
	font-size: 24rpx;
	color: #007AFF;
	font-weight: bold;
}

.machine-item:active .arrow-icon {
	background: rgba(0, 122, 255, 0.2);
	transform: translateX(4rpx);
}

.empty-tip {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx 40rpx;
	text-align: center;
}

.empty-icon {
	font-size: 120rpx;
	margin-bottom: 30rpx;
	opacity: 0.3;
}

.empty-text {
	font-size: 32rpx;
	color: #666;
	margin-bottom: 16rpx;
	font-weight: 500;
}

.empty-desc {
	font-size: 26rpx;
	color: #999;
	line-height: 1.4;
}

.loading-tip {
	display: flex;
	justify-content: center;
	padding: 60rpx;
}

.loading-text {
	font-size: 28rpx;
	color: #666;
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
	.info-row {
		flex-direction: column;
		gap: 8rpx;
	}
	
	.info-item {
		flex: none;
	}
	
	.machine-name {
		font-size: 32rpx;
	}
	
	.machine-model {
		font-size: 24rpx;
	}
}

@media screen and (min-width: 1200rpx) {
	.machine-list {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		gap: 24rpx;
	}
	
	.machine-item {
		margin-bottom: 0;
	}
}

/* 浮动按钮样式 */
.floating-add-btn {
	position: fixed;
	bottom: 120rpx;
	right: 40rpx;
	width: 120rpx;
	height: 120rpx;
	background: linear-gradient(135deg, #007AFF, #0056CC);
	border-radius: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 8rpx 24rpx rgba(0, 122, 255, 0.3);
	z-index: 100;
	transition: all 0.3s ease;
}

.floating-add-btn:active {
	transform: scale(0.95);
	box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.4);
}

.add-icon {
	font-size: 60rpx;
	color: #fff;
	font-weight: 300;
	line-height: 1;
}

/* 弹窗样式 */
.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
	padding: 40rpx;
}

.modal-content {
	background-color: #fff;
	border-radius: 24rpx;
	width: 100%;
	max-width: 600rpx;
	max-height: 80vh;
	overflow: hidden;
	animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
	from {
		opacity: 0;
		transform: translateY(100rpx) scale(0.9);
	}
	to {
		opacity: 1;
		transform: translateY(0) scale(1);
	}
}

.modal-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 40rpx 40rpx 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.modal-close {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 30rpx;
	background-color: #f5f5f5;
	font-size: 40rpx;
	color: #666;
	transition: all 0.2s ease;
}

.modal-close:active {
	background-color: #e0e0e0;
	transform: scale(0.95);
}

.modal-body {
	padding: 40rpx;
	max-height: 60vh;
	overflow-y: auto;
}

.form-group {
	margin-bottom: 40rpx;
}

.form-label {
	display: block;
	font-size: 28rpx;
	color: #333;
	margin-bottom: 16rpx;
	font-weight: 500;
}

.form-input {
	width: 100%;
	height: 80rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 12rpx;
	padding: 0 24rpx;
	font-size: 28rpx;
	color: #333;
	background-color: #fff;
	transition: all 0.3s ease;
}

.form-input:focus {
	border-color: #007AFF;
	box-shadow: 0 0 0 4rpx rgba(0, 122, 255, 0.1);
}

.form-textarea {
	width: 100%;
	min-height: 120rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 12rpx;
	padding: 20rpx 24rpx;
	font-size: 28rpx;
	color: #333;
	background-color: #fff;
	resize: none;
	transition: all 0.3s ease;
}

.form-textarea:focus {
	border-color: #007AFF;
	box-shadow: 0 0 0 4rpx rgba(0, 122, 255, 0.1);
}

.modal-footer {
	display: flex;
	gap: 20rpx;
	padding: 20rpx 40rpx 40rpx;
	border-top: 1rpx solid #f0f0f0;
}

.btn {
	flex: 1;
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 12rpx;
	font-size: 28rpx;
	font-weight: 500;
	transition: all 0.3s ease;
}

.btn-cancel {
	background-color: #f5f5f5;
	color: #666;
}

.btn-cancel:active {
	background-color: #e0e0e0;
	transform: scale(0.98);
}

.btn-primary {
	background: linear-gradient(135deg, #007AFF, #0056CC);
	color: #fff;
}

.btn-primary:active {
	transform: scale(0.98);
	box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);
}

.btn-loading {
	opacity: 0.7;
	pointer-events: none;
}
</style>
<template>
	<view class="container">
		<view class="bg-pattern"></view>
		
		<view class="main-content">
			<view class="logo-box">
				<image class="logo" src="/static/logo.png" mode="aspectFit"></image>
				<text class="title">收割打捆一体机监控系统</text>
			</view>
			
			<view class="form-box">
				<view class="tabs">
					<view 
						class="tab" 
						:class="{ 'active': activeTab === 'login' }" 
						@click="switchTab('login')"
					>
						登录
					</view>
					<view 
						class="tab" 
						:class="{ 'active': activeTab === 'register' }" 
						@click="switchTab('register')"
					>
						注册
					</view>
				</view>
				
				<!-- 登录表单 -->
				<view class="form-content" v-if="activeTab === 'login'">
					<view class="input-group">
						<view class="input-icon">👤</view>
						<input class="input" type="text" v-model="loginForm.username" placeholder="请输入用户名" />
					</view>
					
					<view class="input-group">
						<view class="input-icon">🔒</view>
						<input class="input" type="password" v-model="loginForm.password" placeholder="请输入密码" password />
					</view>
					
					<view class="role-box">
						<view class="role-title">请选择角色：</view>
						<view class="role-list">
							<view class="role-list-item" @click="loginForm.role = 'user'">
								<view class="role-radio">
									<view class="role-radio-inner" v-if="loginForm.role === 'user'"></view>
								</view>
								<text class="role-text">普通用户</text>
							</view>
							<view class="role-list-item" @click="loginForm.role = 'admin'">
								<view class="role-radio">
									<view class="role-radio-inner" v-if="loginForm.role === 'admin'"></view>
								</view>
								<text class="role-text">管理员</text>
							</view>
						</view>
					</view>
					
					<button class="primary-btn" @click="handleLogin" :loading="loading">
						{{ loading ? '登录中...' : '登录' }}
					</button>
					
					<!-- 测试账号提示 -->
					<view class="test-account-tip">
						<text class="tip-title">💡 测试账号</text>
						<text class="tip-desc">管理员: *********** / 123456</text>
					</view>
				</view>
				
				<!-- 注册表单 -->
				<view class="form-content" v-else>
					<view class="input-group">
						<view class="input-icon">📱</view>
						<input class="input" type="text" v-model="registerForm.username" placeholder="请输入手机号作为用户名" maxlength="11" />
					</view>
					
					<view class="input-group">
						<view class="input-icon">🔒</view>
						<input class="input" type="password" v-model="registerForm.password" placeholder="请设置密码" password />
					</view>
					
					<view class="input-group">
						<view class="input-icon">🔑</view>
						<input class="input" type="password" v-model="registerForm.confirmPassword" placeholder="请确认密码" password />
					</view>
					
					<!-- 添加角色选择 -->
					<view class="role-box">
						<view class="role-title">请选择角色：</view>
						<view class="role-list">
							<view class="role-list-item" @click="registerForm.role = 'user'">
								<view class="role-radio">
									<view class="role-radio-inner" v-if="registerForm.role === 'user'"></view>
								</view>
								<text class="role-text">普通用户</text>
							</view>
							<view class="role-list-item" @click="registerForm.role = 'admin'">
								<view class="role-radio">
									<view class="role-radio-inner" v-if="registerForm.role === 'admin'"></view>
								</view>
								<text class="role-text">管理员</text>
							</view>
						</view>
					</view>
					
					<view class="tips-box">
						<text class="tips-text">注册后默认为普通用户权限，无农机访问权限，请联系管理员分配权限。</text>
					</view>
					
					<button class="primary-btn register-btn" @click="handleRegister" :loading="loading">
						{{ loading ? '注册中...' : '注册' }}
					</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
// 导入API服务和工具函数
import { login, register } from '@/common/api/user.js';
import { saveUserInfo } from '@/common/utils/auth.js';

export default {
	data() {
		return {
			activeTab: 'login',
			loginForm: {
				username: '',
				password: '',
				role: 'user'
			},
			registerForm: {
				username: '',
				password: '',
				confirmPassword: '',
				role: 'user'
			},
			loading: false,
			// 本地测试账号（仅用于开发测试）
			testAccounts: [
				{ username: '***********', password: '123456', role: 'admin', name: '系统管理员' }
			]
		}
	},
	onLoad() {
		// 页面加载时，检查是否已登录，如果已登录则跳转到首页
		const userInfo = uni.getStorageSync('userInfo');
		if (userInfo && userInfo.token) {
			uni.switchTab({
				url: '/pages/map/map'
			});
		}
	},
	methods: {
		// 切换登录/注册选项卡
		switchTab(tab) {
			if (this.loading) return; // 防止加载中切换
			this.activeTab = tab;
		},
		
		// 登录处理
		async handleLogin() {
			if (this.loading) return;
			
			// 表单验证
			if (!this.loginForm.username) {
				this.showToast('请输入用户名', 'error');
				return;
			}
			
			if (!this.loginForm.password) {
				this.showToast('请输入密码', 'error');
				return;
			}
			
			this.loading = true;
			
			try {
				// 首先检查本地测试账号
				const testAccount = this.testAccounts.find(account => 
					account.username === this.loginForm.username && 
					account.password === this.loginForm.password
				);
				
				if (testAccount) {
					// 使用本地测试账号登录
					const userInfo = {
						userId: 'test_' + testAccount.username,
						username: testAccount.username,
						role: testAccount.role,
						name: testAccount.name,
						token: 'test_token_' + Date.now(),
						code: testAccount.role === 'admin' ? 1 : (testAccount.role === 'operator' ? 3 : 2)
					};
					
					const saveResult = saveUserInfo(userInfo);
					if (saveResult) {
						this.showToast('登录成功', 'success');
						// 立即跳转，不延迟
						uni.switchTab({
							url: '/pages/map/map'
						});
						return;
					} else {
						this.showToast('保存用户信息失败', 'error');
						return;
					}
				}
				
				// 如果不是测试账号，则调用后端API
				const result = await login(
					this.loginForm.username,
					this.loginForm.password,
					this.loginForm.role
				);
				
				console.log('登录响应:', result);
				
				// 修复：更宽松的成功判断条件
				if (result && (result.code === 200 || result.msg === "操作成功" || result.token)) {
					// 构造用户信息对象
					const userInfo = {
						userId: result.userId || result.id || 'user_' + this.loginForm.username,
						username: result.username || this.loginForm.username,
						role: this.loginForm.role, // 直接使用用户选择的角色
						token: result.token || 'token_' + Date.now(),
						code: this.loginForm.role === 'admin' ? 1 : 2
					};
					
					console.log('准备保存用户信息:', userInfo);
					console.log('登录成功，准备跳转到首页...');
					
					// 保存用户信息
					const saveResult = saveUserInfo(userInfo);
					if (saveResult) {
						this.showToast('登录成功', 'success');
						
						console.log('用户信息保存成功，开始跳转...');
						
						// 立即跳转到首页，不延迟
						uni.switchTab({
							url: '/pages/map/map',
							success: () => {
								console.log('跳转成功');
							},
							fail: (err) => {
								console.error('跳转失败:', err);
								// 如果switchTab失败，尝试使用navigateTo
								uni.navigateTo({
									url: '/pages/map/map'
								});
							}
						});
					} else {
						this.showToast('保存用户信息失败', 'error');
					}
				} else {
					// 登录失败
					const errorMsg = result.msg || result.message || '登录失败';
					this.showToast(errorMsg, 'error');
				}
			} catch (error) {
				console.error('登录失败:', error);
				this.showToast(error.message || '网络错误，请稍后重试', 'error');
			} finally {
				this.loading = false;
			}
		},
		
		// 注册处理
		async handleRegister() {
			// 表单验证
			if (!this.registerForm.username.trim()) {
				this.showToast('请输入手机号作为用户名');
				return;
			}
			
			// 简单的手机号格式验证
			if (!/^1\d{10}$/.test(this.registerForm.username)) {
				this.showToast('请输入正确的手机号');
				return;
			}
			
			if (!this.registerForm.password.trim()) {
				this.showToast('请设置密码');
				return;
			}
			
			if (this.registerForm.password.length < 6) {
				this.showToast('密码长度不能少于6位');
				return;
			}
			
			if (this.registerForm.password !== this.registerForm.confirmPassword) {
				this.showToast('两次输入的密码不一致');
				return;
			}
			
			try {
				this.loading = true;
				
				// 调用注册API
				const result = await register(
					this.registerForm.username, 
					this.registerForm.password,
					this.registerForm.role
				);
				
				this.showToast('注册成功，请登录', 'success');
				
				// 清空注册表单
				this.registerForm = {
					username: '',
					password: '',
					confirmPassword: '',
					role: 'user'
				};
				
				// 立即切换到登录选项卡
				this.activeTab = 'login';
				
			} catch (error) {
				this.showToast(error.message || '注册失败');
			} finally {
				this.loading = false;
			}
		},
		
		showToast(title, icon = 'none') {
			uni.showToast({
				title,
				icon
			});
		}
	}
}
</script>

<style>
/* 主容器样式，使用flex实现多端适配 */
.container {
	position: relative;
	min-height: 100vh;
	width: 100%;
	overflow: hidden;
	background-color: #F5F7FA;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

/* 背景样式 */
.bg-pattern {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 400rpx;
	background: linear-gradient(135deg, #0072ff, #00c6ff);
	border-bottom-left-radius: 50rpx;
	border-bottom-right-radius: 50rpx;
	z-index: 1;
}

.bg-pattern::after {
	content: '';
	position: absolute;
	bottom: -80rpx;
	left: -100rpx;
	width: 300rpx;
	height: 300rpx;
	border-radius: 50%;
	background-color: rgba(255, 255, 255, 0.1);
}

.bg-pattern::before {
	content: '';
	position: absolute;
	top: -120rpx;
	right: -120rpx;
	width: 400rpx;
	height: 400rpx;
	border-radius: 50%;
	background-color: rgba(255, 255, 255, 0.1);
}

/* 主内容区域 */
.main-content {
	position: relative;
	z-index: 2;
	width: 90%;
	max-width: 650rpx; /* 限制最大宽度，提高多端适配性 */
	margin: 0 auto; /* 居中显示 */
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 30rpx 0;
}

/* Logo 区域样式 */
.logo-box {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	margin-bottom: 60rpx;
}

.logo {
	width: 180rpx;
	height: 180rpx;
	margin-bottom: 30rpx; /* 增加与文字间距 */
	background-color: #fff;
	border-radius: 24rpx;
	padding: 15rpx;
	box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.15);
}

.title {
	font-size: 38rpx;
	font-weight: bold;
	color: #fff;
	text-align: center;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
	letter-spacing: 2rpx; /* 增加字间距，提高可读性 */
}

/* 表单容器 */
.form-box {
	width: 100%;
	background-color: #fff;
	border-radius: 24rpx;
	overflow: hidden;
	box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.1);
}

/* 标签页 */
.tabs {
	display: flex;
	background-color: #f8f8f8;
	border-bottom: 1rpx solid #eee;
}

.tab {
	flex: 1;
	height: 96rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 32rpx;
	color: #666;
	position: relative;
	transition: all 0.3s;
}

.tab.active {
	color: #0072ff;
	font-weight: bold;
}

.tab.active::after {
	content: '';
	position: absolute;
	bottom: 0;
	left: 50%;
	transform: translateX(-50%);
	width: 60rpx;
	height: 6rpx;
	background-color: #0072ff;
	border-radius: 3rpx;
}

/* 表单内容 */
.form-content {
	padding: 50rpx 40rpx;
}

.input-group {
	margin-bottom: 35rpx;
	position: relative;
	border-bottom: 1rpx solid #EAEAEA;
	padding-bottom: 8rpx;
}

.input-icon {
	position: absolute;
	left: 0;
	top: 50%;
	transform: translateY(-50%);
	font-size: 36rpx;
	color: #999;
}

.input {
	width: 100%;
	height: 80rpx;
	padding: 0 0 0 60rpx;
	font-size: 30rpx;
	color: #333;
}

/* 角色选择样式 */
.role-box {
	margin: 25rpx 0 40rpx;
}

.role-title {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 20rpx;
}

.role-list {
	margin-top: 20rpx;
}

.role-list-item {
	display: flex;
	align-items: center;
	padding: 18rpx 0;
	transition: all 0.2s;
}

.role-list-item:active {
	background-color: #f9f9f9;
}

.role-radio {
	width: 42rpx;
	height: 42rpx;
	border-radius: 50%;
	border: 2rpx solid #CCCCCC;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 25rpx;
}

.role-radio-inner {
	width: 24rpx;
	height: 24rpx;
	border-radius: 50%;
	background-color: #0072ff;
}

.role-text {
	font-size: 30rpx;
	color: #333;
}

/* 按钮样式 */
.primary-btn {
	width: 100%;
	height: 90rpx;
	line-height: 90rpx;
	font-size: 32rpx;
	border-radius: 45rpx;
	background: linear-gradient(135deg, #0072ff, #00c6ff);
	color: #fff;
	font-weight: bold;
	box-shadow: 0 8rpx 20rpx rgba(0, 114, 255, 0.3);
	margin-bottom: 30rpx;
	border: none;
	transition: all 0.3s;
}

.primary-btn:active {
	transform: translateY(3rpx);
	box-shadow: 0 4rpx 10rpx rgba(0, 114, 255, 0.3);
}

.register-btn {
	background: linear-gradient(135deg, #00b09b, #96c93d);
	box-shadow: 0 8rpx 20rpx rgba(0, 176, 155, 0.3);
}

.register-btn:active {
	box-shadow: 0 4rpx 10rpx rgba(0, 176, 155, 0.3);
}

/* 提示框样式 */
.tips-box {
	margin: 25rpx 0 40rpx;
	padding: 25rpx;
	background-color: #FFF9F0;
	border-radius: 16rpx;
	border-left: 8rpx solid #FFB74D;
}

.tips-text {
	font-size: 24rpx;
	color: #F57C00;
	line-height: 1.6;
}

/* 测试账号提示样式 */
.test-account-tip {
	margin-top: 30rpx;
	padding: 20rpx;
	background: linear-gradient(135deg, #f8f9fa, #e9ecef);
	border-radius: 12rpx;
	border: 1rpx solid #dee2e6;
	text-align: center;
}

.tip-title {
	display: block;
	font-size: 26rpx;
	font-weight: bold;
	color: #495057;
	margin-bottom: 15rpx;
}

.tip-desc {
	display: block;
	font-size: 22rpx;
	color: #6c757d;
	margin-bottom: 8rpx;
	font-family: 'Courier New', monospace;
	background-color: rgba(255, 255, 255, 0.6);
	padding: 4rpx 8rpx;
	border-radius: 6rpx;
	margin: 4rpx 0;
}

/* 响应式适配 */
/* 小屏幕手机 */
@media screen and (max-width: 375px) {
	.main-content {
		width: 92%;
	}
	
	.logo {
		width: 160rpx;
		height: 160rpx;
	}
	
	.title {
		font-size: 34rpx;
	}
	
	.form-content {
		padding: 40rpx 30rpx;
	}
}

/* 大屏幕设备 */
@media screen and (min-width: 768px) {
	.main-content {
		max-width: 750rpx;
	}
	
	.logo {
		width: 200rpx;
		height: 200rpx;
	}
	
	.title {
		font-size: 42rpx;
	}
}

/* H5 和小程序差异适配 */
/* #ifdef H5 */
.container {
	height: 100vh;
}
/* #endif */

/* #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ */
.container {
	height: 100vh;
}
/* #endif */
</style> 
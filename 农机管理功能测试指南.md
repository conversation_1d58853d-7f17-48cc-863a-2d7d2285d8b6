# 农机管理功能测试指南

## 功能概述

本次更新为农机详情页面添加了两个重要的农机管理功能：
1. **修改农机信息**：允许用户编辑农机的基本信息
2. **删除农机**：允许用户从系统中删除农机

这两个功能都已与后端真实API接口联调，不再使用模拟数据。

## 后端接口信息

### 1. 修改农机信息接口
- **请求地址**：`PUT /fram/machine`
- **请求头**：`Authorization: Bearer {token}`
- **请求参数**：
  ```json
  {
    "id": 7,
    "fname": "农机名称",
    "guige": "规格型号", 
    "owner": "车主姓名",
    "company": "生产厂家",
    "brand": "品牌",
    "mobile": "联系电话",
    "memo": "备注信息",
    "username": "用户名"
  }
  ```
- **响应格式**：
  ```json
  {
    "code": 200,
    "msg": "修改成功",
    "data": {...}
  }
  ```

### 2. 删除农机接口
- **请求地址**：`DELETE /fram/machine/{machineId}`
- **请求头**：`Authorization: Bearer {token}`
- **请求参数**：
  ```json
  {
    "username": "用户名"
  }
  ```
- **响应格式**：
  ```json
  {
    "code": 200,
    "msg": "删除成功",
    "data": null
  }
  ```

## 测试场景

### 场景1：修改农机信息
1. **进入详情页面**
   - 从农机列表点击任一农机卡片
   - 进入农机详情页面

2. **打开修改表单**
   - 点击"修改信息"按钮
   - 弹出修改农机信息表单
   - 表单应自动填充当前农机的信息

3. **表单验证测试**
   - 清空必填字段，点击"保存修改"
   - 应显示"请填写所有必填字段"提示
   - 输入错误的手机号格式
   - 应显示"请输入正确的手机号"提示

4. **成功修改测试**
   - 修改农机名称、规格型号等信息
   - 点击"保存修改"按钮
   - 应显示加载状态"保存中..."
   - 成功后应弹出"修改成功"对话框
   - 点击"确定"后表单自动关闭
   - 详情页面应立即显示更新后的信息

5. **失败情况测试**
   - 测试网络异常情况
   - 测试token过期情况（应跳转登录页面）
   - 测试权限不足情况

### 场景2：删除农机
1. **打开删除确认**
   - 在农机详情页面点击"删除农机"按钮
   - 应弹出红色的删除确认对话框
   - 对话框应显示要删除的农机名称

2. **取消删除**
   - 点击"取消"按钮
   - 确认对话框应关闭，返回详情页面

3. **确认删除**
   - 再次点击"删除农机"按钮
   - 点击"确认删除"按钮
   - 应显示加载状态"删除中..."
   - 成功后应弹出"删除成功"对话框
   - 点击"返回列表"应自动跳转回农机列表页面
   - 农机列表应自动刷新，删除的农机不再显示

4. **失败情况测试**
   - 测试网络异常情况
   - 测试token过期情况（应跳转登录页面）
   - 测试权限不足情况（403错误）

### 场景3：数据同步验证
1. **修改后数据同步**
   - 修改农机信息成功后
   - 退出详情页面返回列表
   - 列表中该农机信息应显示最新数据
   - 再次进入详情页面，信息应保持一致

2. **删除后列表刷新**
   - 删除农机成功后
   - 自动返回列表页面
   - 列表应立即刷新，删除的农机消失
   - 农机总数应相应减少

## 调试信息说明

### 修改农机信息调试信息
- `🚜 开始修改农机信息，农机ID: xxx`
- `🚜 修改数据: {fname: "...", guige: "...", ...}`
- `🚜 修改农机信息 API 请求参数: {token: "***有token***", username: "...", machineId: xxx, machineData: {...}}`
- `🚜 修改农机请求URL: http://xxx/fram/machine`
- `🚜 修改农机信息 API 响应: {statusCode: 200, data: {...}}`

### 删除农机调试信息
- `🚜 开始删除农机，农机ID: xxx`
- `🚜 删除农机 API 请求参数: {token: "***有token***", username: "...", machineId: xxx}`
- `🚜 删除农机请求URL: http://xxx/fram/machine/xxx`
- `🚜 删除农机 API 响应: {statusCode: 200, data: {...}}`

### 列表刷新调试信息
- `🚜 收到刷新农机列表事件，重新加载数据`

## 错误处理机制

### 1. 网络错误
- 显示："网络错误或系统异常，请检查网络连接后重试"
- 用户可重新尝试操作

### 2. 认证失败 (401)
- 显示："认证失败，token已过期，请重新登录"
- 自动跳转到登录页面

### 3. 权限不足 (403)
- 显示："您没有权限删除此农机"
- 用户无法继续操作

### 4. 数据验证错误 (400)
- 显示具体的错误信息
- 例如："缺少必需字段: fname, guige"

### 5. 服务器错误 (500)
- 显示："修改/删除农机失败，请稍后重试"
- 用户可稍后重试

## 常见问题排查

### 1. 修改表单不显示当前数据
**原因**：可能是数据传递有问题
**解决**：检查控制台是否有 `🚜 跳转到农机详情，传递数据` 日志

### 2. 修改/删除按钮无响应
**原因**：可能是token过期或用户信息丢失
**解决**：重新登录，确保token有效

### 3. 删除成功但列表未刷新
**原因**：事件监听可能有问题
**解决**：检查控制台是否有 `🚜 收到刷新农机列表事件` 日志

### 4. 接口调用失败
**原因**：后端接口可能未实现或地址错误
**解决**：
- 检查后端是否已实现对应接口
- 确认接口地址是否正确 (`PUT /fram/machine/{id}`, `DELETE /fram/machine/{id}`)
- 检查后端是否支持PUT和DELETE方法

### 5. 权限验证失败
**原因**：后端可能未正确验证用户权限
**解决**：
- 确认后端是否根据username验证用户权限
- 检查是否只允许用户操作自己注册的农机

## 成功标志

### 修改功能成功标志
✅ 表单正确显示当前农机信息  
✅ 表单验证正常工作  
✅ 修改请求成功发送到后端  
✅ 成功响应正确处理  
✅ 页面数据立即更新  
✅ 错误情况正确处理  

### 删除功能成功标志
✅ 删除确认弹窗正确显示  
✅ 删除请求成功发送到后端  
✅ 成功响应正确处理  
✅ 自动返回列表页面  
✅ 列表数据自动刷新  
✅ 错误情况正确处理  

## 注意事项

1. **数据一致性**：修改和删除操作都会影响全局数据，确保前端显示与后端数据保持一致

2. **权限控制**：只有农机的注册用户才能修改或删除该农机

3. **网络处理**：所有网络请求都有超时机制（10秒），并有完整的错误处理

4. **用户体验**：所有操作都有加载状态和明确的成功/失败提示

5. **数据备份**：删除操作不可逆，建议在后端实现软删除机制

通过以上测试，你可以全面验证农机管理功能是否正常工作，确保与后端的联调成功。
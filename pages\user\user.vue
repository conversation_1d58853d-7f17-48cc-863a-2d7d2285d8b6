<template>
	<view class="container">
		<view class="user-header">
			<view class="avatar-box">
				<image class="avatar" src="/static/logo.png" mode="aspectFit"></image>
			</view>
			<view class="user-info">
				<text class="username">{{ userInfo.username || '未登录' }}</text>
				<text class="role-tag" :class="{'admin': isAdmin}">{{ isAdmin ? '管理员' : '普通用户' }}</text>
			</view>
		</view>
		
		<view class="menu-section">
			<view class="menu-title">账号管理</view>
			<view class="menu-list">
				<view class="menu-item" @click="goToHistory">
					<text class="menu-icon">📊</text>
					<text class="menu-label">作业历史</text>
					<text class="menu-arrow">❯</text>
				</view>
				
				<view class="menu-item" v-if="isAdmin" @click="goToManage">
					<text class="menu-icon">🔧</text>
					<text class="menu-label">农机管理</text>
					<text class="menu-arrow">❯</text>
				</view>
				
				<view class="menu-item" @click="showAbout">
					<text class="menu-icon">ℹ️</text>
					<text class="menu-label">关于系统</text>
					<text class="menu-arrow">❯</text>
				</view>
			</view>
		</view>
		
		<view class="menu-section">
			<view class="menu-title">其他</view>
			<view class="menu-list">
				<view class="menu-item" @click="clearCache">
					<text class="menu-icon">🗑️</text>
					<text class="menu-label">清除缓存</text>
					<text class="menu-arrow">❯</text>
				</view>
			</view>
		</view>
		
		<button class="logout-btn" type="default" @click="handleLogout">退出登录</button>
		
		<view class="app-version">
			<text>收割打捆一体机监控系统 v1.0.0</text>
		</view>
	</view>
</template>

<script>
import { getUserInfo, clearUserInfo, isAdmin } from '@/common/utils/auth.js';
import { logout } from '@/common/api/user.js';

export default {
	data() {
		return {
			userInfo: null
		}
	},
	computed: {
		isAdmin() {
			return isAdmin(this.userInfo);
		}
	},
	onLoad() {
		this.userInfo = getUserInfo();
	},
	onShow() {
		// 每次页面显示时重新获取用户信息
		this.userInfo = getUserInfo();
	},
	methods: {
		goToHistory() {
			uni.navigateTo({
				url: '/pages/history/history'
			});
		},
		
		goToManage() {
			uni.navigateTo({
				url: '/pages/manage/manage'
			});
		},
		
		showAbout() {
			uni.showModal({
				title: '关于系统',
				content: '收割打捆一体机监控系统 v1.0.0\n\n本系统用于监控农机工作情况，实时显示位置、作业面积及打捆数量等信息。',
				showCancel: false
			});
		},
		
		clearCache() {
			uni.showLoading({
				title: '正在清除...'
			});
			
			// 模拟清除缓存过程
			setTimeout(() => {
				uni.hideLoading();
				uni.showToast({
					title: '清除成功',
					icon: 'success'
				});
			}, 1000);
		},
		
		async handleLogout() {
			uni.showModal({
				title: '提示',
				content: '确定要退出登录吗？',
				success: async (res) => {
					if (res.confirm) {
						try {
							// 调用登出API
							await logout();
							
							// 清除本地存储的用户信息
							clearUserInfo();
							
							// 跳转到登录页
							uni.reLaunch({
								url: '/pages/login/login'
							});
						} catch (error) {
							uni.showToast({
								title: '退出登录失败',
								icon: 'none'
							});
						}
					}
				}
			});
		}
	}
}
</script>

<style>
.container {
	background-color: #f8f8f8;
	min-height: 100vh;
	padding-bottom: 40rpx;
}

.user-header {
	background-color: #007AFF;
	padding: 60rpx 40rpx;
	display: flex;
	align-items: center;
}

.avatar-box {
	width: 120rpx;
	height: 120rpx;
	background-color: #fff;
	border-radius: 60rpx;
	overflow: hidden;
	margin-right: 30rpx;
}

.avatar {
	width: 100%;
	height: 100%;
}

.user-info {
	flex: 1;
}

.username {
	font-size: 36rpx;
	font-weight: bold;
	color: #fff;
	margin-bottom: 15rpx;
	display: block;
}

.role-tag {
	display: inline-block;
	padding: 4rpx 16rpx;
	background-color: #4cd964;
	font-size: 22rpx;
	color: #fff;
	border-radius: 100rpx;
}

.role-tag.admin {
	background-color: #f0ad4e;
}

.menu-section {
	margin-top: 30rpx;
}

.menu-title {
	padding: 20rpx 30rpx;
	font-size: 28rpx;
	color: #999;
}

.menu-list {
	background-color: #fff;
	border-radius: 12rpx;
	margin: 0 20rpx;
	overflow: hidden;
}

.menu-item {
	display: flex;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #f5f5f5;
}

.menu-item:last-child {
	border-bottom: none;
}

.menu-icon {
	font-size: 36rpx;
	margin-right: 20rpx;
}

.menu-label {
	flex: 1;
	font-size: 28rpx;
	color: #333;
}

.menu-arrow {
	font-size: 24rpx;
	color: #ccc;
}

.logout-btn {
	width: 90%;
	height: 90rpx;
	line-height: 90rpx;
	margin-top: 60rpx;
	margin-left: auto;
	margin-right: auto;
	border-radius: 45rpx;
	background-color: #fff;
	color: #dd524d;
	font-size: 32rpx;
}

.app-version {
	text-align: center;
	margin-top: 60rpx;
	font-size: 24rpx;
	color: #999;
}
</style>
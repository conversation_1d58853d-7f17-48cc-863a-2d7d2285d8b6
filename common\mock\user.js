// 模拟用户数据
export const mockUsers = [
	{
		userId: '001',
		username: 'user1',
		password: '123456',
		role: 'user',
		token: 'user-token-example',
		accessMachines: ['001', '003', '005'] // 普通用户只能查看部分农机
	},
	{
		userId: '002',
		username: 'user2',
		password: '123456',
		role: 'user',
		token: 'user-token-example-2',
		accessMachines: ['002', '004'] // 普通用户只能查看部分农机
	},
	{
		userId: 'admin001',
		username: 'admin',
		password: 'admin123',
		role: 'admin',
		token: 'admin-token-example',
		accessMachines: 'all' // 管理员可查看所有农机
	}
];

// 模拟登录API
export const mockLogin = (username, password, role) => {
	return new Promise((resolve, reject) => {
		// 模拟网络延迟
		setTimeout(() => {
			const user = mockUsers.find(
				u => u.username === username && u.password === password && u.role === role
			);
			
			if (user) {
				// 返回不包含密码的用户信息
				const { password, ...userInfo } = user;
				resolve({
					code: 200,
					message: '登录成功',
					data: userInfo
				});
			} else {
				reject({
					code: 401,
					message: '用户名或密码错误'
				});
			}
		}, 1000);
	});
};

// 模拟获取用户信息API
export const mockGetUserInfo = (token) => {
	return new Promise((resolve, reject) => {
		setTimeout(() => {
			const user = mockUsers.find(u => u.token === token);
			
			if (user) {
				const { password, ...userInfo } = user;
				resolve({
					code: 200,
					message: '获取用户信息成功',
					data: userInfo
				});
			} else {
				reject({
					code: 401,
					message: '登录已过期，请重新登录'
				});
			}
		}, 500);
	});
};

// 模拟注册API
export const mockRegister = (username, password) => {
	return new Promise((resolve, reject) => {
		// 模拟网络延迟
		setTimeout(() => {
			// 检查用户名是否已存在
			const existingUser = mockUsers.find(u => u.username === username);
			
			if (existingUser) {
				reject({
					code: 400,
					message: '该用户名已被注册'
				});
				return;
			}
			
			// 创建新用户
			const newUser = {
				userId: `user${mockUsers.length + 1}`,
				username,
				password,
				role: 'user', // 默认为普通用户
				token: `user-token-${Date.now()}`,
				accessMachines: [] // 新用户默认没有访问权限，需要管理员分配
			};
			
			// 添加到用户列表
			mockUsers.push(newUser);
			
			// 返回不包含密码的用户信息
			const { password: pwd, ...userInfo } = newUser;
			
			resolve({
				code: 200,
				message: '注册成功',
				data: userInfo
			});
		}, 1000);
	});
};
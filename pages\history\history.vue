<template>
	<view class="container">
		<!-- 顶部搜索和筛选区域 -->
		<view class="top-section">
			<view class="search-section">
				<view class="search-box">
					<text class="search-icon">🔍</text>
					<input
						class="search-input"
						placeholder="搜索农机名称、作业地点..."
						v-model="searchKey"
						@input="onSearchInput"
					/>
					<text class="clear-icon" v-if="searchKey" @click="clearSearch">✕</text>
				</view>
			</view>

			<!-- 筛选按钮区域 -->
			<view class="filter-section">
				<scroll-view class="filter-scroll" scroll-x="true" show-scrollbar="false">
					<view class="filter-item"
						:class="{ 'active': currentFilter === 'all' }"
						@click="setFilter('all')">
						<text class="filter-text">全部</text>
						<text class="filter-count">({{ totalCount }})</text>
					</view>
					<view class="filter-item"
						:class="{ 'active': currentFilter === 'recent' }"
						@click="setFilter('recent')">
						<text class="filter-text">最近7天</text>
						<text class="filter-count">({{ recentCount }})</text>
					</view>
					<view class="filter-item"
						:class="{ 'active': currentFilter === 'month' }"
						@click="setFilter('month')">
						<text class="filter-text">本月</text>
						<text class="filter-count">({{ monthCount }})</text>
					</view>
				</scroll-view>
			</view>

			<!-- 美化的刷新按钮 -->
			<view class="refresh-section">
				<view class="refresh-btn" @click="refreshData" :class="{ 'refreshing': loading }">
					<text class="refresh-icon">🔄</text>
					<text class="refresh-text">{{ loading ? '刷新中...' : '刷新数据' }}</text>
				</view>
			</view>
		</view>

		<!-- 历史作业列表 -->
		<view class="history-list" v-if="filteredHistoryList.length > 0">
			<view
				class="history-item"
				v-for="(item, index) in filteredHistoryList"
				:key="item.id || index"
				:style="{ 'animation-delay': (index * 0.1) + 's' }"
			>
				<!-- 状态指示器 -->
				<view class="status-indicator">
					<view class="status-dot completed"></view>
					<view class="status-line completed"></view>
				</view>

				<!-- 历史作业卡片内容 -->
				<view class="history-card">
					<!-- 头部信息 -->
					<view class="history-header">
						<view class="header-left">
							<text class="machine-name">{{ item.orderNjname || '未知农机' }}</text>
							<text class="job-name">{{ item.orderName || '未命名作业' }}</text>
						</view>
						<view class="header-right">
							<text class="status-text completed">已完成</text>
						</view>
					</view>

					<!-- 作业信息 -->
					<view class="history-info">
						<view class="info-row">
							<text class="info-label">作业司机:</text>
							<text class="info-value">{{ item.orderSj || '未知司机' }}</text>
						</view>
						<view class="info-row">
							<text class="info-label">作业地点:</text>
							<text class="info-value">{{ item.orderWhere || '未知地点' }}</text>
						</view>
						<view class="info-row">
							<text class="info-label">作业时间:</text>
							<text class="info-value">{{ formatDate(item.orderTime) }}</text>
						</view>
					</view>

					<!-- 作业数据 -->
					<view class="history-data">
						<view class="data-item">
							<text class="data-value">{{ item.orderMj || '0' }}</text>
							<text class="data-label">作业面积(亩)</text>
						</view>
						<view class="data-item">
							<text class="data-value">{{ item.orderKs || '0' }}</text>
							<text class="data-label">作业数量</text>
						</view>
						<view class="data-item" v-if="item.orderJw">
							<text class="data-value">📍</text>
							<text class="data-label">有位置</text>
						</view>
					</view>

					<!-- 备注信息 -->
					<view class="history-footer" v-if="item.orderMemo">
						<text class="memo-text">备注: {{ item.orderMemo }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 空状态 -->
		<view class="empty-state" v-else-if="!loading">
			<text class="empty-icon">📋</text>
			<text class="empty-title">暂无历史作业记录</text>
			<text class="empty-desc">{{ getEmptyStateDesc() }}</text>
		</view>

		<!-- 加载状态 -->
		<view class="loading-state" v-if="loading">
			<text class="loading-icon">⏳</text>
			<text class="loading-text">正在加载历史记录...</text>
		</view>
	</view>
</template>

<script>
import { getUserInfo } from '@/common/utils/auth.js';
import { getHistoryJobs } from '@/common/api/machines.js';

export default {
	data() {
		return {
			userInfo: null,
			historyList: [],
			searchKey: '',
			currentFilter: 'all',
			loading: false
		}
	},
	computed: {
		// 过滤后的历史记录列表
		filteredHistoryList() {
			let filtered = this.historyList;

			// 按时间过滤
			if (this.currentFilter === 'recent') {
				const sevenDaysAgo = new Date();
				sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
				filtered = filtered.filter(item => {
					const itemDate = new Date(item.orderTime);
					return itemDate >= sevenDaysAgo;
				});
			} else if (this.currentFilter === 'month') {
				const now = new Date();
				const currentMonth = now.getMonth();
				const currentYear = now.getFullYear();
				filtered = filtered.filter(item => {
					const itemDate = new Date(item.orderTime);
					return itemDate.getMonth() === currentMonth && itemDate.getFullYear() === currentYear;
				});
			}

			// 按搜索关键词过滤
			if (this.searchKey) {
				const key = this.searchKey.toLowerCase();
				filtered = filtered.filter(item =>
					(item.orderNjname && item.orderNjname.toLowerCase().includes(key)) ||
					(item.orderName && item.orderName.toLowerCase().includes(key)) ||
					(item.orderWhere && item.orderWhere.toLowerCase().includes(key)) ||
					(item.orderSj && item.orderSj.toLowerCase().includes(key))
				);
			}

			return filtered;
		},

		// 统计数量
		totalCount() {
			return this.historyList.length;
		},

		recentCount() {
			const sevenDaysAgo = new Date();
			sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
			return this.historyList.filter(item => {
				const itemDate = new Date(item.orderTime);
				return itemDate >= sevenDaysAgo;
			}).length;
		},

		monthCount() {
			const now = new Date();
			const currentMonth = now.getMonth();
			const currentYear = now.getFullYear();
			return this.historyList.filter(item => {
				const itemDate = new Date(item.orderTime);
				return itemDate.getMonth() === currentMonth && itemDate.getFullYear() === currentYear;
			}).length;
		}
	},
	onLoad() {
		this.userInfo = getUserInfo();
		this.loadHistoryJobs();
	},
	onShow() {
		// 每次页面显示时重新获取数据
		this.loadHistoryJobs();
	},
	onPullDownRefresh() {
		this.loadHistoryJobs();
	},
	methods: {
		// 加载历史作业记录
		async loadHistoryJobs() {
			if (!this.userInfo) {
				uni.showModal({
					title: '提示',
					content: '请先登录',
					showCancel: false,
					success: () => {
						uni.reLaunch({
							url: '/pages/login/login'
						});
					}
				});
				return;
			}

			this.loading = true;

			try {
				console.log('📋 开始加载历史作业记录...');
				const result = await getHistoryJobs(this.userInfo);

				if (result.code === 200) {
					this.historyList = result.data || [];
					console.log('📋 历史作业记录加载成功，共', this.historyList.length, '条记录');
				} else if (result.code === 401) {
					// 认证失败
					uni.showModal({
						title: '认证失败',
						content: '登录已过期，请重新登录',
						showCancel: false,
						success: () => {
							uni.removeStorageSync('userInfo');
							uni.reLaunch({
								url: '/pages/login/login'
							});
						}
					});
					return;
				} else {
					uni.showToast({
						title: result.message || result.msg || '获取历史记录失败',
						icon: 'none'
					});
					this.historyList = [];
				}
			} catch (error) {
				console.error('❌ 加载历史作业记录失败:', error);
				uni.showToast({
					title: '获取数据失败，请重试',
					icon: 'none'
				});
				this.historyList = [];
			} finally {
				this.loading = false;
				uni.stopPullDownRefresh();
			}
		},

		// 刷新数据
		refreshData() {
			this.loadHistoryJobs();
		},

		// 搜索输入处理
		onSearchInput() {
			// 实时搜索，这里可以添加防抖逻辑
		},

		// 清除搜索
		clearSearch() {
			this.searchKey = '';
		},

		// 设置筛选条件
		setFilter(filter) {
			this.currentFilter = filter;
		},

		// 格式化日期
		formatDate(dateStr) {
			if (!dateStr) return '未知时间';

			try {
				const date = new Date(dateStr);
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				const hours = String(date.getHours()).padStart(2, '0');
				const minutes = String(date.getMinutes()).padStart(2, '0');

				return `${year}-${month}-${day} ${hours}:${minutes}`;
			} catch (error) {
				return '时间格式错误';
			}
		},

		// 获取空状态描述
		getEmptyStateDesc() {
			if (this.currentFilter === 'recent') {
				return '最近7天没有作业记录';
			} else if (this.currentFilter === 'month') {
				return '本月没有作业记录';
			} else if (this.searchKey) {
				return '没有找到匹配的作业记录';
			} else {
				return '还没有任何作业记录，快去创建第一个作业吧！';
			}
		}
	}
}
</script>

<style>
.container {
	background-color: #f8f8f8;
	min-height: 100vh;
}

/* 顶部区域样式 */
.top-section {
	background-color: #fff;
	padding: 20rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 搜索区域 */
.search-section {
	margin-bottom: 20rpx;
}

.search-box {
	display: flex;
	align-items: center;
	background-color: #f5f5f5;
	border-radius: 25rpx;
	padding: 0 20rpx;
	height: 70rpx;
}

.search-icon {
	font-size: 28rpx;
	color: #999;
	margin-right: 15rpx;
}

.search-input {
	flex: 1;
	font-size: 28rpx;
	color: #333;
	background-color: transparent;
}

.clear-icon {
	font-size: 24rpx;
	color: #999;
	padding: 10rpx;
	margin-left: 10rpx;
}

/* 筛选区域 */
.filter-section {
	margin-bottom: 20rpx;
}

.filter-scroll {
	white-space: nowrap;
}

.filter-item {
	display: inline-block;
	padding: 15rpx 25rpx;
	margin-right: 20rpx;
	background-color: #f5f5f5;
	border-radius: 20rpx;
	transition: all 0.3s ease;
}

.filter-item.active {
	background-color: #007AFF;
	color: #fff;
}

.filter-text {
	font-size: 26rpx;
	margin-right: 5rpx;
}

.filter-count {
	font-size: 22rpx;
	opacity: 0.8;
}

/* 刷新按钮 */
.refresh-section {
	display: flex;
	justify-content: center;
}

.refresh-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	background: linear-gradient(135deg, #007AFF 0%, #0056CC 100%);
	color: #fff;
	padding: 15rpx 30rpx;
	border-radius: 25rpx;
	box-shadow: 0 4rpx 15rpx rgba(0, 122, 255, 0.3);
	transition: all 0.3s ease;
}

.refresh-btn.refreshing {
	opacity: 0.7;
}

.refresh-icon {
	font-size: 24rpx;
	margin-right: 10rpx;
	animation: rotate 1s linear infinite;
}

.refresh-btn.refreshing .refresh-icon {
	animation: rotate 1s linear infinite;
}

@keyframes rotate {
	from { transform: rotate(0deg); }
	to { transform: rotate(360deg); }
}

.refresh-text {
	font-size: 26rpx;
}

/* 历史记录列表 */
.history-list {
	padding: 0 20rpx;
}

.history-item {
	display: flex;
	margin-bottom: 20rpx;
	opacity: 0;
	animation: slideInUp 0.6s ease forwards;
}

@keyframes slideInUp {
	from {
		opacity: 0;
		transform: translateY(30rpx);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

/* 状态指示器 */
.status-indicator {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-right: 20rpx;
	padding-top: 10rpx;
}

.status-dot {
	width: 20rpx;
	height: 20rpx;
	border-radius: 50%;
	margin-bottom: 10rpx;
}

.status-line {
	width: 4rpx;
	flex: 1;
	min-height: 60rpx;
}

.status-dot.completed,
.status-line.completed {
	background-color: #4cd964;
}

/* 历史作业卡片 */
.history-card {
	flex: 1;
	background-color: #fff;
	border-radius: 15rpx;
	padding: 25rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid #f0f0f0;
	transition: all 0.3s ease;
}

.history-card:active {
	transform: scale(0.98);
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

/* 头部信息 */
.history-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 20rpx;
	padding-bottom: 15rpx;
	border-bottom: 1rpx solid #f5f5f5;
}

.header-left {
	flex: 1;
}

.machine-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 5rpx;
}

.job-name {
	font-size: 26rpx;
	color: #666;
}

.header-right {
	margin-left: 20rpx;
}

.status-text {
	font-size: 22rpx;
	padding: 8rpx 15rpx;
	border-radius: 12rpx;
	font-weight: 500;
}

.status-text.completed {
	background-color: #e8f5e8;
	color: #4cd964;
}

/* 作业信息 */
.history-info {
	margin-bottom: 20rpx;
}

.info-row {
	display: flex;
	align-items: center;
	margin-bottom: 10rpx;
}

.info-row:last-child {
	margin-bottom: 0;
}

.info-label {
	font-size: 26rpx;
	color: #666;
	width: 140rpx;
	flex-shrink: 0;
}

.info-value {
	font-size: 26rpx;
	color: #333;
	flex: 1;
}

/* 作业数据 */
.history-data {
	display: flex;
	justify-content: space-around;
	padding: 20rpx 0;
	margin-bottom: 15rpx;
	background-color: #f8f9fa;
	border-radius: 10rpx;
}

.data-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	flex: 1;
}

.data-value {
	font-size: 32rpx;
	font-weight: bold;
	color: #007AFF;
	margin-bottom: 8rpx;
}

.data-label {
	font-size: 22rpx;
	color: #999;
}

/* 备注信息 */
.history-footer {
	padding-top: 15rpx;
	border-top: 1rpx solid #f5f5f5;
}

.memo-text {
	font-size: 24rpx;
	color: #666;
	line-height: 1.4;
}

/* 空状态 */
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx 40rpx;
	text-align: center;
}

.empty-icon {
	font-size: 120rpx;
	margin-bottom: 30rpx;
	opacity: 0.3;
}

.empty-title {
	font-size: 32rpx;
	color: #333;
	margin-bottom: 15rpx;
	font-weight: 500;
}

.empty-desc {
	font-size: 26rpx;
	color: #999;
	line-height: 1.4;
}

/* 加载状态 */
.loading-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx 40rpx;
	text-align: center;
}

.loading-icon {
	font-size: 60rpx;
	margin-bottom: 20rpx;
	animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
	0%, 100% { opacity: 0.3; }
	50% { opacity: 1; }
}

.loading-text {
	font-size: 28rpx;
	color: #666;
}
</style>
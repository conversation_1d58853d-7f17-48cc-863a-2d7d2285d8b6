<template>
	<view class="container">
		<view class="header">
			<text class="title">作业历史记录</text>
		</view>
		
		<view class="filter-bar" v-if="allMachines.length > 0">
			<picker @change="onMachineChange" :value="machineIndex" :range="machineNames">
				<view class="picker">
					<text class="picker-label">选择农机：</text>
					<text class="picker-value">{{ currentMachineName }}</text>
					<text class="picker-arrow">▼</text>
				</view>
			</picker>
		</view>
		
		<view class="history-list" v-if="historyList.length > 0">
			<view class="history-item" v-for="(item, index) in historyList" :key="item.id">
				<view class="history-date">{{ item.date }}</view>
				<view class="history-card">
					<view class="history-header">
						<text class="machine-name">{{ getMachineName(item.machineId) }}</text>
						<text class="work-time">{{ item.startTime }} - {{ item.endTime }}</text>
					</view>
					
					<view class="history-data">
						<view class="data-item">
							<text class="data-value">{{ item.workArea }}亩</text>
							<text class="data-label">作业面积</text>
						</view>
						<view class="data-item">
							<text class="data-value">{{ item.packageCount }}个</text>
							<text class="data-label">打捆数量</text>
						</view>
					</view>
					
					<view class="history-footer">
						<view class="location-points">
							<text>位置记录: {{ item.locations.length }}个点</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<view class="empty-tip" v-else>
			<text class="empty-text">暂无历史记录</text>
		</view>
	</view>
</template>

<script>
import { getUserInfo } from '@/common/utils/auth.js';
import { getWorkHistory, getMachineList } from '@/common/api/machines.js';

export default {
	data() {
		return {
			userInfo: null,
			historyList: [],
			allMachines: [],
			selectedMachineId: null,
			machineIndex: 0,
			loading: false
		}
	},
	computed: {
		machineNames() {
			return ['全部农机', ...this.allMachines.map(m => m.name)];
		},
		currentMachineName() {
			return this.machineNames[this.machineIndex] || '全部农机';
		}
	},
	onLoad() {
		this.userInfo = getUserInfo();
		this.loadData();
	},
	onPullDownRefresh() {
		this.loadData();
	},
	methods: {
		async loadData() {
			if (!this.userInfo) {
				return;
			}
			
			this.loading = true;
			
			try {
				// 注释掉农机列表获取，改为按需加载
				// const machinesResult = await getMachineList(this.userInfo);
				// if (machinesResult.code === 200) {
				// 	this.allMachines = machinesResult.data;
				// }
				
				// 直接加载历史记录，不依赖农机列表
				await this.loadHistory();
			} catch (error) {
				uni.showToast({
					title: '获取数据失败',
					icon: 'none'
				});
			} finally {
				this.loading = false;
				uni.stopPullDownRefresh();
			}
		},
		
		async loadHistory() {
			try {
				const result = await getWorkHistory(this.userInfo, this.selectedMachineId);
				if (result.code === 200) {
					// 按日期降序排序
					this.historyList = result.data.sort((a, b) => {
						return new Date(b.date + ' ' + b.startTime) - new Date(a.date + ' ' + a.startTime);
					});
				}
			} catch (error) {
				uni.showToast({
					title: '获取历史记录失败',
					icon: 'none'
				});
				this.historyList = [];
			}
		},
		
		onMachineChange(e) {
			const index = e.detail.value;
			this.machineIndex = index;
			
			// 索引0表示"全部农机"，此时selectedMachineId为null
			this.selectedMachineId = index > 0 ? this.allMachines[index - 1].id : null;
			
			// 重新加载历史记录
			this.loadHistory();
		},
		
		getMachineName(machineId) {
			const machine = this.allMachines.find(m => m.id === machineId);
			return machine ? machine.name : '未知农机';
		}
	}
}
</script>

<style>
.container {
	background-color: #f8f8f8;
	min-height: 100vh;
}

.header {
	background-color: #007AFF;
	padding: 30rpx;
	color: #fff;
}

.title {
	font-size: 36rpx;
	font-weight: bold;
}

.filter-bar {
	padding: 20rpx 30rpx;
	background-color: #fff;
	margin-bottom: 20rpx;
}

.picker {
	display: flex;
	align-items: center;
}

.picker-label {
	font-size: 28rpx;
	color: #666;
	margin-right: 10rpx;
}

.picker-value {
	flex: 1;
	font-size: 28rpx;
	color: #333;
}

.picker-arrow {
	font-size: 24rpx;
	color: #999;
	margin-left: 10rpx;
}

.history-list {
	padding: 20rpx;
}

.history-item {
	margin-bottom: 30rpx;
}

.history-date {
	font-size: 24rpx;
	color: #999;
	padding: 0 20rpx;
	margin-bottom: 10rpx;
}

.history-card {
	background-color: #fff;
	border-radius: 12rpx;
	padding: 20rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.history-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding-bottom: 20rpx;
	border-bottom: 1rpx solid #f5f5f5;
	margin-bottom: 20rpx;
}

.machine-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.work-time {
	font-size: 24rpx;
	color: #999;
}

.history-data {
	display: flex;
	padding-bottom: 20rpx;
	margin-bottom: 20rpx;
	border-bottom: 1rpx solid #f5f5f5;
}

.data-item {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.data-value {
	font-size: 36rpx;
	font-weight: bold;
	color: #007AFF;
	margin-bottom: 5rpx;
}

.data-label {
	font-size: 24rpx;
	color: #999;
}

.history-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.location-points {
	font-size: 24rpx;
	color: #999;
}

.empty-tip {
	padding: 100rpx 0;
	text-align: center;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
}
</style>
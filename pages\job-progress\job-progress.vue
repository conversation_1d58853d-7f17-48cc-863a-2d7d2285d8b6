<template>
	<view class="container">
		<view class="header">
			<view class="back-btn" @click="goBack">
				<text class="back-icon">‹</text>
			</view>
			<text class="header-title">{{ jobInfo.jobName || '作业进行中' }}</text>
			<view class="header-right"></view>
		</view>
		
		<view class="content">
			<!-- 美化的作业状态卡片 -->
			<view class="hero-status-card" :class="statusClass">
				<view class="status-background"></view>
				<view class="status-content">
					<view class="status-main">
						<view class="status-indicator-modern">
							<view class="status-pulse" :class="statusClass"></view>
							<view class="status-ring" :class="statusClass"></view>
							<text class="status-icon">{{ getStatusIcon() }}</text>
						</view>

						<view class="status-info">
							<text class="status-title">{{ statusText }}</text>
							<text class="job-name">{{ jobInfo.jobName || '未命名作业' }}</text>
							<text class="machine-name">{{ machineInfo.fname || '农机设备' }}</text>
						</view>
					</view>

					<view class="time-display">
						<text class="time-label">运行时长</text>
						<text class="time-value">{{ formatJobTime }}</text>
					</view>
				</view>
			</view>

			<!-- 作业详细信息卡片 -->
			<view class="job-info-card">
				<view class="info-header">
					<view class="header-icon">📋</view>
					<text class="header-title">作业详情</text>
					<view class="status-badge" :class="statusClass">
						<text class="status-text">{{ statusText }}</text>
					</view>
				</view>

				<view class="info-content">
					<view class="info-grid">
						<view class="info-item primary">
							<view class="info-icon">🚜</view>
							<view class="info-details">
								<text class="info-label">作业农机</text>
								<text class="info-value">{{ jobInfo.orderNjname || '未知农机' }}</text>
							</view>
						</view>

						<view class="info-item">
							<view class="info-icon">🌾</view>
							<view class="info-details">
								<text class="info-label">作业名称</text>
								<text class="info-value">{{ jobInfo.orderName || '未命名作业' }}</text>
							</view>
						</view>

						<view class="info-item">
							<view class="info-icon">📅</view>
							<view class="info-details">
								<text class="info-label">作业日期</text>
								<text class="info-value">{{ formatJobDate }}</text>
							</view>
						</view>

						<view class="info-item">
							<view class="info-icon">👨‍🌾</view>
							<view class="info-details">
								<text class="info-label">作业司机</text>
								<text class="info-value">{{ jobInfo.orderSj || '待分配' }}</text>
							</view>
						</view>

						<view class="info-item highlight">
							<view class="info-icon">📍</view>
							<view class="info-details">
								<text class="info-label">作业地点</text>
								<text class="info-value location">{{ jobInfo.orderWhere || '待分配地点' }}</text>
							</view>
						</view>

						<view class="info-item" v-if="jobInfo.orderMemo">
							<view class="info-icon">📝</view>
							<view class="info-details">
								<text class="info-label">备注信息</text>
								<text class="info-value memo">{{ jobInfo.orderMemo }}</text>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 实时数据展示卡片 -->
			<view class="data-showcase-card">
				<view class="showcase-header">
					<view class="header-icon">📊</view>
					<text class="header-title">实时数据</text>
					<view class="refresh-status" :class="{ 'refreshing': isRefreshing }">
						<text class="refresh-icon">🔄</text>
					</view>
				</view>

				<view class="metrics-grid">
					<view class="metric-card primary">
						<view class="metric-icon">📦</view>
						<view class="metric-content">
							<text class="metric-value">{{ realTimeData.packageCount || 0 }}</text>
							<text class="metric-unit">个</text>
							<text class="metric-label">打捆数量</text>
						</view>
						<view class="metric-trend">
							<text class="trend-indicator">↗</text>
						</view>
					</view>

					<view class="metric-card secondary">
						<view class="metric-icon">⏱️</view>
						<view class="metric-content">
							<text class="metric-value">{{ workDuration }}</text>
							<text class="metric-unit">分钟</text>
							<text class="metric-label">作业时长</text>
						</view>
						<view class="metric-trend">
							<text class="trend-indicator">⏰</text>
						</view>
					</view>
				</view>

				<view class="data-details">
					<view class="detail-item">
						<view class="detail-icon">🕐</view>
						<view class="detail-content">
							<text class="detail-label">最后更新</text>
							<text class="detail-value">{{ lastUpdateTime }}</text>
						</view>
					</view>

					<view class="detail-item">
						<view class="detail-icon">📍</view>
						<view class="detail-content">
							<text class="detail-label">当前状态</text>
							<text class="detail-value" :class="statusClass">{{ statusText }}</text>
						</view>
					</view>
				</view>

				<view class="auto-refresh-info">
					<view class="refresh-indicator-modern" :class="{ 'active': isRefreshing }">
						<text class="refresh-dot"></text>
					</view>
					<text class="refresh-text">数据每30秒自动更新</text>
				</view>
			</view>

			<!-- 现代化操作按钮 -->
			<view class="control-panel">
				<view class="control-grid">
					<!-- 开始作业按钮 -->
					<view
						v-if="jobInfo.status === 'pending'"
						class="control-item control-start"
						:class="{ 'disabled': actionLoading }"
						@click="showStartConfirm"
					>
						<view class="control-icon-wrapper">
							<text class="control-icon">🚀</text>
						</view>
						<view class="control-content">
							<text class="control-title">开始作业</text>
							<text class="control-desc">启动农机开始作业</text>
						</view>
					</view>

					<!-- 停止作业按钮 -->
					<view
						v-if="jobInfo.status === 'running'"
						class="control-item control-stop"
						:class="{ 'disabled': actionLoading }"
						@click="showStopConfirm"
					>
						<view class="control-icon-wrapper">
							<text class="control-icon">⏹</text>
						</view>
						<view class="control-content">
							<text class="control-title">停止作业</text>
							<text class="control-desc">结束并统计作业</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 开始作业确认弹窗 -->
		<view class="modal-overlay" v-if="showStartModal" @click="hideStartModal">
			<view class="modal-content" @click.stop>
				<view class="modal-header">
					<text class="modal-title">开始作业</text>
				</view>

				<view class="modal-body">
					<text class="modal-text">确定要开始当前作业吗？</text>
					<text class="modal-hint">开始后农机将进入作业状态</text>
				</view>

				<view class="modal-footer">
					<view class="btn btn-cancel" @click="hideStartModal">
						<text>取消</text>
					</view>
					<view class="btn btn-primary" @click="submitStartJob" :class="{ 'btn-loading': actionLoading }">
						<text v-if="!actionLoading">确认开始</text>
						<text v-else>启动中...</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 停止作业确认弹窗 -->
		<view class="modal-overlay" v-if="showStopModal" @click="hideStopModal">
			<view class="modal-content" @click.stop>
				<view class="modal-header">
					<text class="modal-title">停止作业</text>
				</view>

				<view class="modal-body">
					<text class="modal-text">确定要停止当前作业吗？</text>
					<text class="modal-hint">停止后将显示作业面积统计</text>
				</view>

				<view class="modal-footer">
					<view class="btn btn-cancel" @click="hideStopModal">
						<text>取消</text>
					</view>
					<view class="btn btn-danger" @click="submitStopJob" :class="{ 'btn-loading': actionLoading }">
						<text v-if="!actionLoading">确认停止</text>
						<text v-else>停止中...</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 作业完成结果弹窗 -->
		<view class="modal-overlay" v-if="showResultModal" @click="hideResultModal">
			<view class="modal-content result-modal enhanced" @click.stop>
				<view class="modal-header celebration">
					<view class="celebration-icon">🎉</view>
					<text class="modal-title">作业完成</text>
					<view class="quality-badge" :class="getQualityBadgeClass()">
						<text class="quality-text">{{ jobResult.performance || '良好' }}</text>
					</view>
				</view>

				<view class="modal-body">
					<!-- 核心数据展示 -->
					<view class="result-highlights">
						<view class="highlight-item primary">
							<view class="highlight-icon">🌾</view>
							<view class="highlight-content">
								<text class="highlight-value">{{ jobResult.workArea || 0 }}</text>
								<text class="highlight-unit">亩</text>
								<text class="highlight-label">作业面积</text>
							</view>
						</view>

						<view class="highlight-item secondary">
							<view class="highlight-icon">📦</view>
							<view class="highlight-content">
								<text class="highlight-value">{{ jobResult.packageCount || 0 }}</text>
								<text class="highlight-unit">个</text>
								<text class="highlight-label">打捆数量</text>
							</view>
						</view>
					</view>

					<!-- 详细统计 -->
					<view class="result-details">
						<view class="detail-section">
							<text class="section-title">作业统计</text>

							<view class="detail-grid">
								<view class="detail-item">
									<text class="detail-label">作业时长</text>
									<text class="detail-value">{{ Math.round((jobResult.totalWorkingTime || 0) / 60 * 10) / 10 }}小时</text>
								</view>

								<view class="detail-item">
									<text class="detail-label">平均速度</text>
									<text class="detail-value">{{ jobResult.averageSpeed || 0 }}km/h</text>
								</view>

								<view class="detail-item">
									<text class="detail-label">作业效率</text>
									<text class="detail-value">{{ jobResult.efficiency || 0 }}个/小时</text>
								</view>

								<view class="detail-item">
									<text class="detail-label">燃油消耗</text>
									<text class="detail-value">{{ jobResult.fuelConsumption || 0 }}L</text>
								</view>
							</view>
						</view>

						<!-- 质量评分 -->
						<view class="detail-section" v-if="jobResult.qualityScore">
							<text class="section-title">质量评分</text>
							<view class="quality-score">
								<view class="score-circle" :class="getScoreClass()">
									<text class="score-number">{{ jobResult.qualityScore }}</text>
									<text class="score-total">/100</text>
								</view>
								<text class="score-description">{{ getScoreDescription() }}</text>
							</view>
						</view>

						<!-- 建议 -->
						<view class="detail-section" v-if="jobResult.recommendations && jobResult.recommendations.length > 0">
							<text class="section-title">优化建议</text>
							<view class="recommendations">
								<view
									class="recommendation-item"
									v-for="(recommendation, index) in jobResult.recommendations"
									:key="index"
								>
									<view class="recommendation-icon">💡</view>
									<text class="recommendation-text">{{ recommendation }}</text>
								</view>
							</view>
						</view>
					</view>
				</view>

				<view class="modal-footer enhanced">
					<view class="btn btn-secondary" @click="shareJobResult">
						<text>分享结果</text>
					</view>
					<view class="btn btn-primary" @click="goBackToDetail">
						<text>返回详情</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { getUserInfo } from '@/common/utils/auth.js';
import { startJob, stopJob, getJobRealTimeData } from '@/common/api/machines.js';

export default {
	data() {
		return {
			jobId: '',
			machineId: '',
			userInfo: null,

			jobInfo: {
				jobName: '',
				status: 'running', // running, paused, completed
				startTime: null,
				// 作业详细信息（根据后端字段结构）
				orderNjname: '', // 作业农机名称
				orderName: '', // 作业名称
				orderMj: '', // 作业面积
				orderTime: null, // 作业时间
				orderSj: '', // 作业司机
				orderJw: '', // 作业经纬度
				orderKs: 0, // 作业数量
				orderWhere: '', // 作业地点
				orderMemo: '', // 作业备注
				njno: '', // 农机授权码
				orderId: '' // 后端返回的订单唯一标识符
			},
			machineInfo: {},
			realTimeData: {
				packageCount: 0,
				updateTime: null
			},
			jobResult: {
				workArea: 0,
				packageCount: 0
			},
			// 控制状态
			isRefreshing: false,
			actionLoading: false,
			showStartModal: false,
			showStopModal: false,
			showResultModal: false,
			// 定时器
			refreshTimer: null,
			timeUpdateTimer: null, // 计时器定时器
			startTime: null
		}
	},
	
	computed: {
		statusClass() {
			switch (this.jobInfo.status) {
				case 'pending':
					return 'status-pending';
				case 'running':
					return 'status-running';
				case 'completed':
					return 'status-completed';
				default:
					return 'status-unknown';
			}
		},

		statusText() {
			switch (this.jobInfo.status) {
				case 'pending':
					return '待开始';
				case 'running':
					return '进行中';
				case 'completed':
					return '已完成';
				default:
					return '未知状态';
			}
		},
		
		formatJobTime() {
			if (!this.startTime) return '--';
			const now = new Date();
			const start = new Date(this.startTime);
			const totalSeconds = Math.floor((now - start) / 1000);
			const minutes = Math.floor(totalSeconds / 60);
			const seconds = totalSeconds % 60;
			return `${minutes}分${seconds.toString().padStart(2, '0')}秒`;
		},
		
		workDuration() {
			if (!this.startTime) return '0';
			const now = new Date();
			const start = new Date(this.startTime);
			const diff = Math.floor((now - start) / 1000 / 60); // 分钟
			return diff.toString();
		},
		
		finalWorkDuration() {
			if (!this.jobResult.endTime || !this.startTime) return '0';
			const end = new Date(this.jobResult.endTime);
			const start = new Date(this.startTime);
			const diff = Math.floor((end - start) / 1000 / 60); // 分钟
			return diff.toString();
		},

		lastUpdateTime() {
			if (!this.realTimeData.updateTime) return '--';
			const time = new Date(this.realTimeData.updateTime);
			return time.toLocaleTimeString('zh-CN', {
				hour12: false,
				hour: '2-digit',
				minute: '2-digit',
				second: '2-digit'
			});
		},

		// 格式化作业日期
		formatJobDate() {
			if (!this.jobInfo.orderTime) return '--';
			const date = new Date(this.jobInfo.orderTime);
			return date.toLocaleDateString('zh-CN', {
				year: 'numeric',
				month: '2-digit',
				day: '2-digit'
			}).replace(/\//g, '-');
		}
	},
	
	onLoad(options) {
		this.jobId = options.jobId || '';
		this.machineId = options.machineId || '';

		if (!this.jobId || !this.machineId) {
			uni.showToast({
				title: '参数错误',
				icon: 'none'
			});
			setTimeout(() => {
				uni.navigateBack();
			}, 1500);
			return;
		}

		this.initPage();
	},
	
	onShow() {
		// 页面显示时开始定时刷新和计时器
		this.startRefreshTimer();
		this.startTimeUpdateTimer();
	},
	
	onHide() {
		// 页面隐藏时停止定时刷新和计时器
		this.stopRefreshTimer();
		this.stopTimeUpdateTimer();
	},
	
	onUnload() {
		// 页面卸载时清理定时器
		this.stopRefreshTimer();
		this.stopTimeUpdateTimer();
	},
	
	methods: {
		async initPage() {
			this.userInfo = getUserInfo();
			if (!this.userInfo) {
				uni.showToast({
					title: '请先登录',
					icon: 'none'
				});
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
				return;
			}

			// 初始化作业信息 - 从页面参数或全局数据获取
			this.loadJobInfo();

			this.startTime = new Date().toISOString();

			// 作业状态初始化为待开始
			this.jobInfo.status = 'pending';

			// 开始定时刷新和计时器
			this.startRefreshTimer();
			this.startTimeUpdateTimer();
		},

		// 加载作业信息
		loadJobInfo() {
			try {
				const globalData = getApp().globalData;
				console.log('📋 加载作业信息，全局数据:', globalData);
				
				if (globalData && globalData.currentJobInfo) {
					const jobData = globalData.currentJobInfo;
					console.log('📋 获取到作业数据:', jobData);
					
					// 设置作业信息
					Object.assign(this.jobInfo, {
						jobName: jobData.jobName || `作业_${this.jobId}`,
						orderNjname: jobData.orderNjname || '未知农机',
						orderName: jobData.orderName || jobData.jobName || '未命名作业',
						orderTime: jobData.orderTime ? new Date(jobData.orderTime) : new Date(),
						orderSj: jobData.orderSj || '待分配司机',
						orderWhere: jobData.orderWhere || '待分配地点',
						orderMemo: jobData.orderMemo || '',
						orderMj: jobData.orderMj || '',
						orderJw: jobData.orderJw || '',
						orderKs: jobData.orderKs || 0,
						njno: jobData.njno || '', // 农机授权码
						orderId: jobData.orderId || '' // 后端返回的订单唯一标识符
					});
					
					// 设置农机信息
					if (jobData.machineInfo) {
						this.machineInfo = jobData.machineInfo;
					}
				} else {
					console.warn('⚠️ 未找到作业信息，使用默认值');
					// 使用默认值
					this.jobInfo.jobName = `作业_${this.jobId}`;
					this.jobInfo.orderName = this.jobInfo.jobName;
					this.jobInfo.orderTime = new Date();
					this.jobInfo.orderSj = '待分配司机';
					this.jobInfo.orderWhere = '待分配地点';
				}
			} catch (error) {
				console.error('❌ 加载作业信息失败:', error);
				// 使用默认值作为兜底
				this.jobInfo.jobName = `作业_${this.jobId}`;
				this.jobInfo.orderName = this.jobInfo.jobName;
				this.jobInfo.orderTime = new Date();
			}
		},
		


		// 开始定时刷新
		startRefreshTimer() {
			this.stopRefreshTimer(); // 先清除之前的定时器
			this.refreshTimer = setInterval(() => {
				this.refreshRealTimeData();
			}, 30000); // 30秒刷新一次

			// 立即执行一次
			this.refreshRealTimeData();
		},

		// 停止定时刷新
		stopRefreshTimer() {
			if (this.refreshTimer) {
				clearInterval(this.refreshTimer);
				this.refreshTimer = null;
			}
		},

		// 开始计时器
		startTimeUpdateTimer() {
			this.stopTimeUpdateTimer(); // 先清除之前的定时器
			this.timeUpdateTimer = setInterval(() => {
				// 强制更新计算属性，每秒刷新一次时间显示
				this.$forceUpdate();
			}, 1000); // 1秒更新一次
		},

		// 停止计时器
		stopTimeUpdateTimer() {
			if (this.timeUpdateTimer) {
				clearInterval(this.timeUpdateTimer);
				this.timeUpdateTimer = null;
			}
		},

		// 刷新实时数据
		async refreshRealTimeData() {
			if (this.jobInfo.status !== 'running') {
				return; // 只有运行中才刷新数据
			}

			this.isRefreshing = true;

			try {
				console.log('📊 正在获取实时作业数据（打捆数量orderKs）...');
				const result = await getJobRealTimeData(this.userInfo, this.jobId);
				
				if (result.code === 200) {
					console.log('📊 实时数据获取成功:', result.data);
					
					// 更新实时数据 - 只获取打捆数量orderKs
					this.realTimeData = {
						packageCount: result.data.orderKs || 0, // 作业数量（打捆数量）
						updateTime: new Date().toISOString()
					};
					
					// 更新作业数量
					if (result.data.orderKs !== undefined) {
						this.jobInfo.orderKs = result.data.orderKs;
					}
				} else {
					console.error('获取实时数据失败:', result.message);
				}
			} catch (error) {
				console.error('❌ 获取实时数据异常:', error);
			} finally {
				setTimeout(() => {
					this.isRefreshing = false;
				}, 500);
			}
		},

		// 计算工作时长（分钟）
		calculateWorkingTime() {
			if (!this.startTime) return 0;
			const now = new Date();
			const start = new Date(this.startTime);
			return Math.floor((now - start) / 1000 / 60);
		},

		// 显示开始作业确认弹窗
		showStartConfirm() {
			this.showStartModal = true;
		},

		// 隐藏开始作业确认弹窗
		hideStartModal() {
			this.showStartModal = false;
		},

		// 提交开始作业
		async submitStartJob() {
			this.actionLoading = true;
			this.hideStartModal();

			try {
				console.log('🚀 开始作业，发送start指令给后端');

				// 准备作业信息，包含njno和orderId
				const jobInfo = {
					njno: this.jobInfo.njno || '',
					orderId: this.jobInfo.orderId || '',
					id: this.jobInfo.orderId || ''
				};

				console.log('🚀 传递给后端的作业信息:', jobInfo);

				// 发送start指令给后端
				const result = await startJob(this.userInfo, this.jobId, jobInfo);

				if (result.code === 200) {
					console.log('🚀 作业开始API响应成功:', result.data);

					// 检查设备连接状态
					const connected = result.data?.connected;
					const connectionStatus = result.data?.connectionStatus;

					console.log('🔗 设备连接状态:', connectionStatus, '是否连接:', connected);

					if (connected === false || connectionStatus === 'failed') {
						// 连接失败，不能开始作业
						uni.showModal({
							title: '设备连接失败',
							content: '无法连接到农机设备，请检查设备状态后重试',
							showCancel: false,
							confirmText: '确定'
						});
						return;
					}

					// 连接成功，更新作业状态
					this.jobInfo.status = 'running';
					this.startTime = result.data.startTime || new Date().toISOString();

					// 开始定时刷新和计时
					this.startRefreshTimer();
					this.startTimeUpdateTimer();

					uni.showToast({
						title: '设备连接成功，作业已开始',
						icon: 'success'
					});
				} else if (result.code === 401) {
					// 认证失败
					uni.showModal({
						title: '认证失败',
						content: result.message || 'token已过期，请重新登录',
						showCancel: false,
						confirmText: '重新登录',
						success: () => {
							uni.reLaunch({
								url: '/pages/login/login'
							});
						}
					});
				} else {
					uni.showToast({
						title: result.message || '开始作业失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('❌ 开始作业失败:', error);
				uni.showToast({
					title: '开始作业失败，请重试',
					icon: 'none'
				});
			} finally {
				this.actionLoading = false;
			}
		},



		// 显示停止确认弹窗
		showStopConfirm() {
			this.showStopModal = true;
		},

		// 隐藏停止确认弹窗
		hideStopModal() {
			this.showStopModal = false;
		},

		// 提交停止作业
		async submitStopJob() {
			this.actionLoading = true;

			try {
				const result = await stopJob(this.userInfo, this.jobId);
				if (result.code === 200) {
					this.jobInfo.status = 'completed';
					this.stopRefreshTimer(); // 停止刷新
					this.stopTimeUpdateTimer(); // 停止计时

					// 设置作业结果（使用后端返回的数据）
					this.jobResult = {
						workArea: result.data.workArea || 0,
						packageCount: result.data.packageCount || this.realTimeData.packageCount || 0,
						endTime: result.data.endTime || new Date().toISOString(),
						totalWorkingTime: this.calculateWorkingTime(),
						...result.data // 包含后端返回的其他数据
					};

					this.hideStopModal();

					// 显示作业完成的庆祝效果
					setTimeout(() => {
						this.showResultModal = true;
						this.celebrateJobCompletion();
					}, 500);
				} else if (result.code === 401) {
					// 认证失败
					uni.showModal({
						title: '认证失败',
						content: result.message || 'token已过期，请重新登录',
						showCancel: false,
						confirmText: '重新登录',
						success: () => {
							uni.reLaunch({
								url: '/pages/login/login'
							});
						}
					});
				} else {
					uni.showToast({
						title: result.message || '停止作业失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('停止作业失败:', error);
				uni.showToast({
					title: '停止作业失败，请重试',
					icon: 'none'
				});
			} finally {
				this.actionLoading = false;
			}
		},



		// 庆祝作业完成
		celebrateJobCompletion() {
			// 显示完成提示
			uni.showToast({
				title: '🎉 作业完成！',
				icon: 'success',
				duration: 2000
			});

			// 可以在这里添加更多庆祝效果，比如动画等
			console.log('作业完成庆祝效果');
		},

		// 隐藏结果弹窗
		hideResultModal() {
			this.showResultModal = false;
		},

		// 返回详情页面
		goBackToDetail() {
			this.hideResultModal();
			uni.navigateBack({
				delta: 2 // 返回到农机详情页面
			});
		},

		// 返回上一页
		goBack() {
			if (this.jobInfo.status === 'running') {
				uni.showModal({
					title: '提示',
					content: '作业正在进行中，确定要离开吗？',
					success: (res) => {
						if (res.confirm) {
							this.stopRefreshTimer();
							uni.navigateBack();
						}
					}
				});
			} else {
				this.stopRefreshTimer();
				uni.navigateBack();
			}
		},

		// 获取状态图标
		getStatusIcon() {
			switch (this.jobInfo.status) {
				case 'pending':
					return '⏳';
				case 'running':
					return '🚜';
				case 'completed':
					return '✅';
				default:
					return '❓';
			}
		},

		// 获取质量徽章样式
		getQualityBadgeClass() {
			const score = this.jobResult.qualityScore || 0;
			if (score >= 95) return 'excellent';
			if (score >= 90) return 'good';
			if (score >= 80) return 'average';
			return 'poor';
		},

		// 获取评分样式
		getScoreClass() {
			const score = this.jobResult.qualityScore || 0;
			if (score >= 95) return 'score-excellent';
			if (score >= 90) return 'score-good';
			if (score >= 80) return 'score-average';
			return 'score-poor';
		},

		// 获取评分描述
		getScoreDescription() {
			const score = this.jobResult.qualityScore || 0;
			if (score >= 95) return '优秀的作业质量，效率和精度都很出色！';
			if (score >= 90) return '良好的作业表现，继续保持！';
			if (score >= 80) return '作业质量合格，还有提升空间。';
			return '作业质量需要改进，建议优化操作方式。';
		},

		// 分享作业结果
		shareJobResult() {
			const shareText = `🚜 作业完成报告
📊 作业面积: ${this.jobResult.workArea}亩
📦 打捆数量: ${this.jobResult.packageCount}个
⏱️ 作业时长: ${Math.round((this.jobResult.totalWorkingTime || 0) / 60 * 10) / 10}小时
🏆 质量评分: ${this.jobResult.qualityScore}/100 (${this.jobResult.performance})
⚡ 作业效率: ${this.jobResult.efficiency}个/小时

#智慧农机 #精准作业`;

			// 复制到剪贴板
			this.copyToClipboard(shareText);
		},

		// 复制到剪贴板
		copyToClipboard(text) {
			uni.setClipboardData({
				data: text,
				success: () => {
					uni.showToast({
						title: '作业报告已复制到剪贴板',
						icon: 'success',
						duration: 2000
					});
				},
				fail: () => {
					uni.showToast({
						title: '复制失败',
						icon: 'none'
					});
				}
			});
		}
	}
}
</script>

<style scoped>
.container {
	min-height: 100vh;
	background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
}

.header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 30rpx;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20rpx);
	border-bottom: 1rpx solid rgba(255, 255, 255, 0.2);
	position: sticky;
	top: 0;
	z-index: 100;
}

.back-btn {
	width: 80rpx;
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 40rpx;
	background: linear-gradient(135deg, #667eea, #764ba2);
	transition: all 0.3s ease;
	box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}

.back-btn:active {
	transform: scale(0.95);
	box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.4);
}

.back-icon {
	font-size: 36rpx;
	color: #fff;
	font-weight: bold;
}

.header-title {
	font-size: 32rpx;
	font-weight: 700;
	color: #333;
	flex: 1;
	text-align: center;
	letter-spacing: -0.5rpx;
}

.header-right {
	width: 80rpx;
}

.content {
	padding: 30rpx;
	margin-top: -20rpx;
	position: relative;
	z-index: 2;
}

/* 美化的状态卡片 */
.hero-status-card {
	background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
	border-radius: 32rpx;
	margin-bottom: 40rpx;
	box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.1);
	border: 1rpx solid rgba(255, 255, 255, 0.8);
	overflow: hidden;
	position: relative;
}

.status-background {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	opacity: 0.05;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.hero-status-card.status-running .status-background {
	background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}



.hero-status-card.status-pending .status-background {
	background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

.hero-status-card.status-completed .status-background {
	background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.status-content {
	position: relative;
	z-index: 2;
	padding: 40rpx;
}

.status-main {
	display: flex;
	align-items: center;
	gap: 32rpx;
	margin-bottom: 32rpx;
}

.status-indicator-modern {
	position: relative;
	width: 120rpx;
	height: 120rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.status-pulse {
	position: absolute;
	width: 100%;
	height: 100%;
	border-radius: 50%;
	opacity: 0.3;
}

.status-pulse.status-running {
	background: #34C759;
	animation: statusPulse 2s infinite;
}



.status-pulse.status-pending {
	background: #FF9500;
	animation: statusPulse 3s infinite;
}

.status-pulse.status-completed {
	background: #007AFF;
}

@keyframes statusPulse {
	0% { transform: scale(1); opacity: 0.3; }
	50% { transform: scale(1.1); opacity: 0.1; }
	100% { transform: scale(1); opacity: 0.3; }
}

.status-ring {
	position: absolute;
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	border: 4rpx solid;
}

.status-ring.status-running {
	border-color: #34C759;
}



.status-ring.status-pending {
	border-color: #FF9500;
}

.status-ring.status-completed {
	border-color: #007AFF;
}

.status-icon {
	font-size: 48rpx;
	z-index: 3;
}

.status-info {
	flex: 1;
}

.status-title {
	font-size: 40rpx;
	font-weight: 800;
	margin-bottom: 12rpx;
	display: block;
	letter-spacing: -1rpx;
}

.status-title {
	color: #34C759;
}



.status-completed .status-title {
	color: #007AFF;
}

.job-name {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 8rpx;
	display: block;
}

.machine-name {
	font-size: 26rpx;
	color: #666;
	display: block;
}

.time-display {
	text-align: center;
	padding: 24rpx;
	background: rgba(255, 255, 255, 0.6);
	border-radius: 20rpx;
	backdrop-filter: blur(10rpx);
}

.time-label {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 8rpx;
	display: block;
}

.time-value {
	font-size: 48rpx;
	font-weight: 800;
	color: #333;
	display: block;
}

/* 作业详细信息卡片 */
.job-info-card {
	background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
	border-radius: 32rpx;
	margin-bottom: 40rpx;
	box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.1);
	border: 1rpx solid rgba(255, 255, 255, 0.8);
	overflow: hidden;
}

.info-header {
	display: flex;
	align-items: center;
	gap: 16rpx;
	padding: 32rpx 32rpx 24rpx;
	background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
	border-bottom: 1rpx solid rgba(14, 165, 233, 0.1);
	position: relative;
}

.info-header .header-icon {
	font-size: 32rpx;
	width: 60rpx;
	height: 60rpx;
	background: linear-gradient(135deg, #0ea5e9, #0284c7);
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 4rpx 12rpx rgba(14, 165, 233, 0.3);
}

.info-header .header-title {
	font-size: 32rpx;
	font-weight: 700;
	color: #1a1a1a;
	flex: 1;
	letter-spacing: -0.5rpx;
}

.status-badge {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 22rpx;
	font-weight: 600;
}

.status-badge.status-running {
	background: rgba(52, 199, 89, 0.2);
	color: #34C759;
	border: 2rpx solid rgba(52, 199, 89, 0.3);
}



.status-badge.status-pending {
	background: rgba(255, 149, 0, 0.2);
	color: #FF9500;
	border: 2rpx solid rgba(255, 149, 0, 0.3);
}

.status-badge.status-completed {
	background: rgba(0, 122, 255, 0.2);
	color: #007AFF;
	border: 2rpx solid rgba(0, 122, 255, 0.3);
}

.status-text {
	font-size: 20rpx;
	font-weight: 600;
}

.info-content {
	padding: 32rpx;
}

.info-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 20rpx;
}

.info-item {
	display: flex;
	align-items: flex-start;
	gap: 16rpx;
	padding: 24rpx;
	background: rgba(14, 165, 233, 0.02);
	border-radius: 20rpx;
	border: 1rpx solid rgba(14, 165, 233, 0.08);
	transition: all 0.3s ease;
}

.info-item.primary {
	grid-column: 1 / -1;
	background: linear-gradient(135deg, rgba(14, 165, 233, 0.1), rgba(2, 132, 199, 0.05));
	border: 2rpx solid rgba(14, 165, 233, 0.2);
}

.info-item.highlight {
	grid-column: 1 / -1;
	background: linear-gradient(135deg, rgba(168, 85, 247, 0.1), rgba(147, 51, 234, 0.05));
	border: 2rpx solid rgba(168, 85, 247, 0.2);
}

.info-icon {
	font-size: 28rpx;
	width: 48rpx;
	height: 48rpx;
	background: linear-gradient(135deg, #0ea5e9, #0284c7);
	border-radius: 12rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-shrink: 0;
	box-shadow: 0 2rpx 8rpx rgba(14, 165, 233, 0.2);
}

.info-item.primary .info-icon {
	background: linear-gradient(135deg, #34C759, #30a46c);
}

.info-item.highlight .info-icon {
	background: linear-gradient(135deg, #a855f7, #9333ea);
}

.info-details {
	flex: 1;
	min-width: 0;
}

.info-label {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 6rpx;
	display: block;
	font-weight: 500;
}

.info-value {
	font-size: 28rpx;
	color: #333;
	font-weight: 600;
	line-height: 1.3;
	word-break: break-all;
}

.info-value.location {
	color: #a855f7;
	font-weight: 700;
}

.info-value.memo {
	font-size: 26rpx;
	color: #666;
	font-weight: 500;
	line-height: 1.4;
}

.info-item.primary .info-value {
	color: #34C759;
	font-size: 30rpx;
}

/* 数据展示卡片 */
.data-showcase-card {
	background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
	border-radius: 32rpx;
	margin-bottom: 40rpx;
	box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.1);
	border: 1rpx solid rgba(255, 255, 255, 0.8);
	overflow: hidden;
}

.showcase-header {
	display: flex;
	align-items: center;
	gap: 16rpx;
	padding: 32rpx 32rpx 24rpx;
	background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
	border-bottom: 1rpx solid rgba(0, 122, 255, 0.1);
}

.header-icon {
	font-size: 32rpx;
	width: 60rpx;
	height: 60rpx;
	background: linear-gradient(135deg, #007AFF, #0056CC);
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.3);
}

.header-title {
	font-size: 32rpx;
	font-weight: 700;
	color: #1a1a1a;
	flex: 1;
	letter-spacing: -0.5rpx;
}

.refresh-status {
	width: 40rpx;
	height: 40rpx;
	border-radius: 20rpx;
	background: rgba(0, 122, 255, 0.1);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
}

.refresh-status.refreshing {
	background: rgba(52, 199, 89, 0.2);
	animation: rotate 2s linear infinite;
}

@keyframes rotate {
	from { transform: rotate(0deg); }
	to { transform: rotate(360deg); }
}

.refresh-icon {
	font-size: 20rpx;
}

.metrics-grid {
	display: flex;
	gap: 24rpx;
	padding: 32rpx;
	margin-bottom: 24rpx;
}

.metric-card {
	flex: 1;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 24rpx;
	padding: 32rpx;
	color: #fff;
	position: relative;
	overflow: hidden;
	box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
}

.metric-card.secondary {
	background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
	box-shadow: 0 8rpx 24rpx rgba(240, 147, 251, 0.3);
}

.metric-icon {
	font-size: 40rpx;
	margin-bottom: 16rpx;
	display: block;
}

.metric-content {
	margin-bottom: 16rpx;
}

.metric-value {
	font-size: 56rpx;
	font-weight: 800;
	line-height: 1;
	display: inline-block;
}

.metric-unit {
	font-size: 24rpx;
	font-weight: 500;
	margin-left: 8rpx;
	opacity: 0.8;
}

.metric-label {
	font-size: 24rpx;
	margin-top: 8rpx;
	display: block;
	opacity: 0.9;
}

.metric-trend {
	position: absolute;
	top: 24rpx;
	right: 24rpx;
}

.trend-indicator {
	font-size: 24rpx;
	opacity: 0.7;
}

.data-details {
	padding: 0 32rpx 24rpx;
}

.detail-item {
	display: flex;
	align-items: center;
	gap: 16rpx;
	margin-bottom: 20rpx;
	padding: 20rpx;
	background: rgba(0, 122, 255, 0.02);
	border-radius: 16rpx;
	border: 1rpx solid rgba(0, 122, 255, 0.08);
}

.detail-item:last-child {
	margin-bottom: 0;
}

.detail-icon {
	font-size: 24rpx;
	width: 48rpx;
	height: 48rpx;
	background: linear-gradient(135deg, #007AFF, #0056CC);
	border-radius: 12rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-shrink: 0;
}

.detail-content {
	flex: 1;
}

.detail-label {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 4rpx;
	display: block;
}

.detail-value {
	font-size: 28rpx;
	color: #333;
	font-weight: 600;
}

.detail-value.status-running {
	color: #34C759;
}



.detail-value.status-completed {
	color: #007AFF;
}

.auto-refresh-info {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 12rpx;
	padding: 24rpx 32rpx 32rpx;
	border-top: 1rpx solid rgba(0, 0, 0, 0.06);
}

.refresh-indicator-modern {
	width: 16rpx;
	height: 16rpx;
	border-radius: 50%;
	background: #8E8E93;
	transition: all 0.3s ease;
}

.refresh-indicator-modern.active {
	background: #34C759;
	animation: pulse 1s infinite;
}

@keyframes pulse {
	0%, 100% { opacity: 1; transform: scale(1); }
	50% { opacity: 0.5; transform: scale(1.1); }
}

.refresh-text {
	font-size: 24rpx;
	color: #8E8E93;
}

/* 控制面板样式 */
.control-panel {
	background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
	border-radius: 32rpx;
	margin-bottom: 40rpx;
	box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.1);
	border: 1rpx solid rgba(255, 255, 255, 0.8);
	overflow: hidden;
}

.control-grid {
	padding: 32rpx;
}

.control-item {
	display: flex;
	align-items: center;
	gap: 24rpx;
	padding: 32rpx;
	margin-bottom: 24rpx;
	border-radius: 24rpx;
	transition: all 0.3s ease;
	cursor: pointer;
	position: relative;
	overflow: hidden;
}

.control-item:last-child {
	margin-bottom: 0;
}

.control-item.disabled {
	opacity: 0.5;
	pointer-events: none;
}



.control-start {
	background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
	color: #0056b3;
	box-shadow: 0 8rpx 24rpx rgba(79, 172, 254, 0.3);
}



.control-stop {
	background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
	color: #8B0000;
	box-shadow: 0 8rpx 24rpx rgba(255, 154, 158, 0.3);
}

.control-item:active {
	transform: translateY(-2rpx) scale(0.98);
}

.control-icon-wrapper {
	width: 80rpx;
	height: 80rpx;
	background: rgba(255, 255, 255, 0.3);
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	backdrop-filter: blur(10rpx);
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.control-icon {
	font-size: 40rpx;
	font-weight: bold;
}

.control-content {
	flex: 1;
}

.control-title {
	font-size: 36rpx;
	font-weight: 700;
	margin-bottom: 8rpx;
	display: block;
	letter-spacing: -0.5rpx;
}

.control-desc {
	font-size: 26rpx;
	opacity: 0.8;
	display: block;
	line-height: 1.4;
}

/* 弹窗样式 */
.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
	padding: 40rpx;
}

.modal-content {
	background-color: #fff;
	border-radius: 24rpx;
	width: 100%;
	max-width: 600rpx;
	overflow: hidden;
	animation: modalSlideIn 0.3s ease-out;
}

.result-modal {
	max-width: 500rpx;
}

@keyframes modalSlideIn {
	from {
		opacity: 0;
		transform: translateY(100rpx) scale(0.9);
	}
	to {
		opacity: 1;
		transform: translateY(0) scale(1);
	}
}

.modal-header {
	padding: 40rpx 40rpx 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	text-align: center;
}

.modal-body {
	padding: 40rpx;
}

.modal-text {
	font-size: 28rpx;
	color: #333;
	text-align: center;
	margin-bottom: 16rpx;
}

.modal-hint {
	font-size: 24rpx;
	color: #8E8E93;
	text-align: center;
}

.modal-footer {
	display: flex;
	gap: 20rpx;
	padding: 20rpx 40rpx 40rpx;
	border-top: 1rpx solid #f0f0f0;
}

.btn {
	flex: 1;
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 12rpx;
	font-size: 28rpx;
	font-weight: 500;
	transition: all 0.3s ease;
}

.btn-cancel {
	background-color: #f5f5f5;
	color: #666;
}

.btn-cancel:active {
	background-color: #e0e0e0;
	transform: scale(0.98);
}

.btn-primary {
	background: linear-gradient(135deg, #007AFF, #0056CC);
	color: #fff;
}

.btn-primary:active {
	transform: scale(0.98);
	box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);
}

.btn-danger {
	background: linear-gradient(135deg, #FF3B30, #D70015);
	color: #fff;
}

.btn-danger:active {
	transform: scale(0.98);
	box-shadow: 0 2rpx 8rpx rgba(255, 59, 48, 0.3);
}

.btn-loading {
	opacity: 0.7;
	pointer-events: none;
}

.full-width {
	flex: none;
	width: 100%;
}

/* 结果显示样式 */
.result-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.result-item:last-child {
	border-bottom: none;
}

.result-label {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}

.result-value-container {
	display: flex;
	align-items: baseline;
	gap: 8rpx;
}

.result-value {
	font-size: 36rpx;
	font-weight: bold;
	color: #007AFF;
}

.result-unit {
	font-size: 24rpx;
	color: #8E8E93;
}

/* 增强的结果弹窗样式 */
.result-modal.enhanced {
	max-width: 700rpx;
}

.modal-header.celebration {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #fff;
	text-align: center;
	position: relative;
	overflow: hidden;
}

.celebration-icon {
	font-size: 60rpx;
	margin-bottom: 16rpx;
	display: block;
	animation: bounce 2s infinite;
}

@keyframes bounce {
	0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
	40% { transform: translateY(-10rpx); }
	60% { transform: translateY(-5rpx); }
}

.quality-badge {
	position: absolute;
	top: 20rpx;
	right: 20rpx;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 22rpx;
	font-weight: 600;
}

.quality-badge.excellent {
	background: rgba(52, 199, 89, 0.2);
	color: #34C759;
	border: 2rpx solid rgba(52, 199, 89, 0.3);
}

.quality-badge.good {
	background: rgba(0, 122, 255, 0.2);
	color: #007AFF;
	border: 2rpx solid rgba(0, 122, 255, 0.3);
}

.quality-badge.average {
	background: rgba(255, 149, 0, 0.2);
	color: #FF9500;
	border: 2rpx solid rgba(255, 149, 0, 0.3);
}

.quality-badge.poor {
	background: rgba(255, 59, 48, 0.2);
	color: #FF3B30;
	border: 2rpx solid rgba(255, 59, 48, 0.3);
}

.result-highlights {
	display: flex;
	gap: 24rpx;
	margin-bottom: 40rpx;
}

.highlight-item {
	flex: 1;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 24rpx;
	padding: 32rpx;
	color: #fff;
	text-align: center;
	box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
}

.highlight-item.secondary {
	background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
	box-shadow: 0 8rpx 24rpx rgba(240, 147, 251, 0.3);
}

.highlight-icon {
	font-size: 40rpx;
	margin-bottom: 16rpx;
	display: block;
}

.highlight-value {
	font-size: 56rpx;
	font-weight: 800;
	line-height: 1;
	display: inline-block;
}

.highlight-unit {
	font-size: 24rpx;
	font-weight: 500;
	margin-left: 8rpx;
	opacity: 0.8;
}

.highlight-label {
	font-size: 24rpx;
	margin-top: 8rpx;
	display: block;
	opacity: 0.9;
}

.result-details {
	margin-bottom: 20rpx;
}

.detail-section {
	margin-bottom: 32rpx;
}

.detail-section:last-child {
	margin-bottom: 0;
}

.section-title {
	font-size: 28rpx;
	font-weight: 700;
	color: #333;
	margin-bottom: 20rpx;
	display: block;
	padding-bottom: 12rpx;
	border-bottom: 2rpx solid #f0f0f0;
}

.detail-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 20rpx;
}

.detail-item {
	background: rgba(0, 122, 255, 0.02);
	border-radius: 16rpx;
	padding: 20rpx;
	border: 1rpx solid rgba(0, 122, 255, 0.08);
}

.detail-label {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 8rpx;
	display: block;
}

.detail-value {
	font-size: 28rpx;
	color: #333;
	font-weight: 600;
}

.quality-score {
	display: flex;
	align-items: center;
	gap: 24rpx;
	padding: 24rpx;
	background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
	border-radius: 20rpx;
	border: 1rpx solid rgba(0, 122, 255, 0.1);
}

.score-circle {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	position: relative;
	border: 6rpx solid;
}

.score-excellent {
	border-color: #34C759;
	background: rgba(52, 199, 89, 0.1);
}

.score-good {
	border-color: #007AFF;
	background: rgba(0, 122, 255, 0.1);
}

.score-average {
	border-color: #FF9500;
	background: rgba(255, 149, 0, 0.1);
}

.score-poor {
	border-color: #FF3B30;
	background: rgba(255, 59, 48, 0.1);
}

.score-number {
	font-size: 36rpx;
	font-weight: 800;
	color: #333;
	line-height: 1;
}

.score-total {
	font-size: 20rpx;
	color: #666;
	margin-top: 4rpx;
}

.score-description {
	flex: 1;
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
}

.recommendations {
	background: rgba(255, 193, 7, 0.05);
	border-radius: 16rpx;
	padding: 20rpx;
	border: 1rpx solid rgba(255, 193, 7, 0.2);
}

.recommendation-item {
	display: flex;
	align-items: flex-start;
	gap: 12rpx;
	margin-bottom: 16rpx;
}

.recommendation-item:last-child {
	margin-bottom: 0;
}

.recommendation-icon {
	font-size: 20rpx;
	margin-top: 4rpx;
	flex-shrink: 0;
}

.recommendation-text {
	font-size: 26rpx;
	color: #666;
	line-height: 1.4;
	flex: 1;
}

.modal-footer.enhanced {
	gap: 16rpx;
}

.btn-secondary {
	background: linear-gradient(135deg, #f5f5f5, #e0e0e0);
	color: #666;
}

.btn-secondary:active {
	background: linear-gradient(135deg, #e0e0e0, #d0d0d0);
	transform: scale(0.98);
}
</style>

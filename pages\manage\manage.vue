<template>
	<view class="container">
		<view class="header">
			<view class="back-btn" @click="goBack">
				<text>←</text>
			</view>
			<text class="title">农机管理</text>
		</view>
		
		<view class="content">
			<view class="section">
				<view class="section-header">
					<text class="section-title">农机状态概览</text>
				</view>
				<view class="status-cards">
					<view class="status-card" :class="'total'">
						<text class="status-value">{{ stats.totalMachines || 0 }}</text>
						<text class="status-label">总数量</text>
					</view>
					<view class="status-card" :class="'working'">
						<text class="status-value">{{ stats.statusStats.working || 0 }}</text>
						<text class="status-label">作业中</text>
					</view>
					<view class="status-card" :class="'idle'">
						<text class="status-value">{{ stats.statusStats.idle || 0 }}</text>
						<text class="status-label">空闲</text>
					</view>
					<view class="status-card" :class="'maintenance'">
						<text class="status-value">{{ stats.statusStats.maintenance || 0 }}</text>
						<text class="status-label">维护中</text>
					</view>
				</view>
			</view>
			
			<view class="section">
				<view class="section-header">
					<text class="section-title">农机列表</text>
					<text class="admin-tip">（管理员可查看全部农机）</text>
				</view>
				
				<view class="machine-list" v-if="machines.length > 0">
					<view class="machine-item" v-for="machine in machines" :key="machine.id" @click="goToDetail(machine.id)">
						<view class="machine-status" :class="machine.status"></view>
						<view class="machine-info">
							<view class="machine-header">
								<text class="machine-name">{{ machine.name }}</text>
								<text class="machine-status-text" :class="machine.status">{{ getStatusText(machine.status) }}</text>
							</view>
							<view class="machine-subinfo">
								<text class="machine-model">{{ machine.model }}</text>
								<text class="machine-driver">驾驶员: {{ machine.driver.name }}</text>
							</view>
							<view class="machine-data">
								<text class="data-item">作业面积: {{ machine.workArea }}亩</text>
								<text class="data-item">打捆数: {{ machine.packageCount }}个</text>
							</view>
						</view>
						<text class="arrow">❯</text>
					</view>
				</view>
				
				<view class="empty-tip" v-else>
					<text>暂无农机数据</text>
				</view>
			</view>
			
			<view class="section">
				<view class="section-header">
					<text class="section-title">管理功能</text>
				</view>
				
				<view class="function-list">
					<view class="function-item" @click="showHistoryPage">
						<text class="function-icon">📊</text>
						<text class="function-name">作业历史统计</text>
					</view>
					<view class="function-item" @click="showNotImplemented">
						<text class="function-icon">📱</text>
						<text class="function-name">远程控制</text>
					</view>
					<view class="function-item" @click="showNotImplemented">
						<text class="function-icon">⚙️</text>
						<text class="function-name">系统设置</text>
					</view>
					<view class="function-item" @click="showNotImplemented">
						<text class="function-icon">👥</text>
						<text class="function-name">用户管理</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { getUserInfo, isAdmin } from '@/common/utils/auth.js';
import { getMachineList, getStatistics } from '@/common/api/machines.js';

export default {
	data() {
		return {
			userInfo: null,
			machines: [],
			stats: {
				totalMachines: 0,
				totalWorkArea: 0,
				totalPackageCount: 0,
				statusStats: {
					working: 0,
					idle: 0,
					maintenance: 0
				}
			},
			loading: false
		}
	},
	onLoad() {
		this.userInfo = getUserInfo();
		
		// 检查是否为管理员
		if (!isAdmin(this.userInfo)) {
			uni.showToast({
				title: '您没有管理员权限',
				icon: 'none'
			});
			setTimeout(() => {
				this.goBack();
			}, 1500);
			return;
		}
		
		this.loadData();
	},
	methods: {
		async loadData() {
			if (!this.userInfo) {
				return;
			}
			
			this.loading = true;
			
			try {
				// 注释掉农机列表获取，改为按需加载
				// const machinesResult = await getMachineList(this.userInfo);
				// if (machinesResult.code === 200) {
				// 	this.machines = machinesResult.data;
				// }
				
				// 只获取统计数据
				const statsResult = await getStatistics(this.userInfo);
				if (statsResult.code === 200) {
					this.stats = statsResult.data;
				}
			} catch (error) {
				uni.showToast({
					title: '获取数据失败',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},
		
		getStatusText(status) {
			const statusMap = {
				'working': '作业中',
				'idle': '空闲',
				'maintenance': '维护中'
			};
			
			return statusMap[status] || '未知';
		},
		
		goBack() {
			uni.navigateBack();
		},
		
		goToDetail(machineId) {
			uni.navigateTo({
				url: `/pages/machine-detail/machine-detail?id=${machineId}`
			});
		},
		
		showHistoryPage() {
			uni.navigateTo({
				url: '/pages/history/history'
			});
		},
		
		showNotImplemented() {
			uni.showToast({
				title: '功能开发中',
				icon: 'none'
			});
		}
	}
}
</script>

<style>
.container {
	background-color: #f8f8f8;
	min-height: 100vh;
}

.header {
	background-color: #007AFF;
	padding: 60rpx 30rpx 30rpx;
	color: #fff;
	display: flex;
	align-items: center;
}

.back-btn {
	font-size: 36rpx;
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	color: #fff;
}

.title {
	font-size: 36rpx;
	font-weight: bold;
}

.content {
	padding: 20rpx;
}

.section {
	margin-bottom: 30rpx;
}

.section-header {
	margin-bottom: 20rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.admin-tip {
	font-size: 24rpx;
	color: #999;
	margin-left: 10rpx;
}

.status-cards {
	display: flex;
	justify-content: space-between;
}

.status-card {
	flex: 1;
	background-color: #fff;
	border-radius: 12rpx;
	padding: 20rpx;
	margin: 0 5rpx;
	text-align: center;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	display: flex;
	flex-direction: column;
	align-items: center;
}

.status-card.total {
	border-top: 4rpx solid #007AFF;
}

.status-card.working {
	border-top: 4rpx solid #4cd964;
}

.status-card.idle {
	border-top: 4rpx solid #f0ad4e;
}

.status-card.maintenance {
	border-top: 4rpx solid #dd524d;
}

.status-value {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
}

.status-label {
	font-size: 24rpx;
	color: #999;
}

.machine-list {
	background-color: #fff;
	border-radius: 12rpx;
	overflow: hidden;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.machine-item {
	display: flex;
	padding: 20rpx;
	border-bottom: 1rpx solid #f5f5f5;
}

.machine-item:last-child {
	border-bottom: none;
}

.machine-status {
	width: 10rpx;
	background-color: #999;
	margin-right: 20rpx;
}

.machine-status.working {
	background-color: #4cd964;
}

.machine-status.idle {
	background-color: #f0ad4e;
}

.machine-status.maintenance {
	background-color: #dd524d;
}

.machine-info {
	flex: 1;
}

.machine-header {
	display: flex;
	justify-content: space-between;
	margin-bottom: 10rpx;
}

.machine-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.machine-status-text {
	font-size: 24rpx;
	padding: 2rpx 10rpx;
	border-radius: 4rpx;
	color: #fff;
}

.machine-status-text.working {
	background-color: #4cd964;
}

.machine-status-text.idle {
	background-color: #f0ad4e;
}

.machine-status-text.maintenance {
	background-color: #dd524d;
}

.machine-subinfo {
	display: flex;
	margin-bottom: 10rpx;
}

.machine-model {
	font-size: 24rpx;
	color: #999;
	margin-right: 20rpx;
}

.machine-driver {
	font-size: 24rpx;
	color: #666;
}

.machine-data {
	font-size: 24rpx;
	color: #666;
}

.data-item {
	margin-right: 20rpx;
}

.arrow {
	display: flex;
	align-items: center;
	color: #ccc;
	font-size: 36rpx;
}

.empty-tip {
	text-align: center;
	padding: 50rpx 0;
	color: #999;
	background-color: #fff;
	border-radius: 12rpx;
}

.function-list {
	display: flex;
	flex-wrap: wrap;
}

.function-item {
	width: 25%;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 20rpx 0;
}

.function-icon {
	font-size: 48rpx;
	margin-bottom: 10rpx;
}

.function-name {
	font-size: 24rpx;
	color: #333;
}
</style>
<template>
	<view class="container">
		<view class="map-container">
			<!-- 百度静态地图显示区域 -->
			<view class="baidu-map-container">
				<image 
					v-if="mapImageUrl" 
					:src="mapImageUrl" 
					class="baidu-map-image" 
					mode="aspectFit"
					@load="onMapLoad"
					@error="onMapError"
				/>
				<view v-else class="map-loading">
					<text class="loading-text">地图加载中...</text>
				</view>
			</view>
			
			<!-- 地图控制按钮 -->
			<view class="map-controls">
				<view class="control-btn" @click="refreshMap">
					<text class="iconfont icon-refresh"></text>
					<text class="btn-text">刷新地图</text>
				</view>
				<view class="control-btn" @click="toggleMapSize">
					<text class="iconfont icon-resize"></text>
					<text class="btn-text">{{isLargeMap ? '缩小' : '放大'}}</text>
				</view>
			</view>
			
			<!-- 小程序平台提示 -->
			<!-- #ifndef H5 -->
			<view class="map-placeholder">
				<text class="map-text">地图功能已优化为静态地图</text>
				<text class="map-desc">显示农机分布和统计信息</text>
			</view>
			<!-- #endif -->
		</view>
		
		<!-- 统计信息栏 -->
		<view class="statistics-bar">
			<view class="stat-item">
				<text class="stat-value">{{stats.totalMachines || 0}}</text>
				<text class="stat-label">总农机数</text>
			</view>
			<view class="stat-item">
				<text class="stat-value">{{stats.statusStats.working || 0}}</text>
				<text class="stat-label">正在作业</text>
			</view>
			<view class="stat-item">
				<text class="stat-value">{{formatArea(stats.totalWorkArea)}}</text>
				<text class="stat-label">总作业面积(亩)</text>
			</view>
			<view class="stat-item">
				<text class="stat-value">{{stats.totalPackageCount || 0}}</text>
				<text class="stat-label">总打捆数</text>
			</view>
		</view>
		
		<!-- 农机状态图例 -->
		<view class="legend-bar">
			<view class="legend-item">
				<view class="legend-color working"></view>
				<text class="legend-text">作业中</text>
			</view>
			<view class="legend-item">
				<view class="legend-color idle"></view>
				<text class="legend-text">空闲</text>
			</view>
			<view class="legend-item">
				<view class="legend-color maintenance"></view>
				<text class="legend-text">维护中</text>
			</view>
		</view>
	</view>
</template>

<script>
import { getUserInfo } from '@/common/utils/auth.js';
import { getStatistics, getMachineListNew } from '@/common/api/machines.js';
import { generateMachinesMapUrl } from '@/common/utils/mapHelper.js';
import mapConfig from '@/common/config/map.js';

export default {
	data() {
		return {
			userInfo: null,
			machines: [],
			mapImageUrl: '',
			isLargeMap: false,
			stats: {
				totalWorkArea: 0,
				totalPackageCount: 0,
				totalMachines: 0,
				statusStats: {
					working: 0,
					idle: 0,
					maintenance: 0
				}
			}
		}
	},
	
	onLoad() {
		this.initData();
	},
	
	onShow() {
		// 页面显示时刷新数据
		this.loadMachinesAndStats();
	},
	
	methods: {
		// 初始化数据
		async initData() {
			try {
				this.userInfo = getUserInfo();
				if (!this.userInfo) {
					uni.showToast({
						title: '请先登录',
						icon: 'none'
					});
					setTimeout(() => {
						uni.reLaunch({
							url: '/pages/login/login'
						});
					}, 1500);
					return;
				}
				
				await this.loadMachinesAndStats();
			} catch (error) {
				console.error('❌ 初始化数据失败:', error);
				uni.showToast({
					title: '数据加载失败',
					icon: 'none'
				});
			}
		},
		
		// 加载农机数据和统计信息
		async loadMachinesAndStats() {
			try {
				console.log('🗺️ 开始加载农机数据和地图...');
				
				// 加载农机列表
				const result = await getMachineListNew(this.userInfo);
				
				if (result.code === 200 && result.data) {
					this.machines = result.data;
					console.log('🚜 加载农机数据成功:', this.machines);
					
					// 计算统计信息
					this.calculateStats();
					
					// 生成地图
					this.generateMap();
				} else if (result.code === 401) {
					// 处理认证失败，清除用户信息并跳转登录
					console.error('🔐 认证失败，token已过期');
					const { clearUserInfo } = await import('@/common/utils/auth.js');
					clearUserInfo();
					
					uni.showModal({
						title: '登录已过期',
						content: '您的登录状态已过期，请重新登录',
						showCancel: false,
						confirmText: '去登录',
						success: () => {
							uni.reLaunch({
								url: '/pages/login/login'
							});
						}
					});
				} else {
					console.error('❌ 加载农机数据失败:', result.message);
					uni.showToast({
						title: result.message || '加载失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('❌ 加载数据异常:', error);
				uni.showToast({
					title: '网络连接失败',
					icon: 'none'
				});
			}
		},
		
		// 计算统计信息
		calculateStats() {
			const stats = {
				totalWorkArea: 0,
				totalPackageCount: 0,
				totalMachines: this.machines.length,
				statusStats: {
					working: 0,
					idle: 0,
					maintenance: 0
				}
			};
			
			this.machines.forEach(machine => {
				// 累计作业面积和打捆数
				stats.totalWorkArea += machine.workArea || 0;
				stats.totalPackageCount += machine.packageCount || 0;
				
				// 统计状态分布
				const status = machine.status || 'idle';
				if (stats.statusStats[status] !== undefined) {
					stats.statusStats[status]++;
				} else {
					stats.statusStats.idle++;
				}
			});
			
			this.stats = stats;
			console.log('📊 统计信息计算完成:', this.stats);
		},
		
		// 生成百度地图
		generateMap() {
			try {
				console.log('🗺️ 开始生成百度静态地图...');
				
				// 设置地图尺寸
				const mapSize = this.isLargeMap ? { width: 600, height: 450 } : { width: 400, height: 300 };
				
				// 生成包含农机标记的地图URL
				this.mapImageUrl = generateMachinesMapUrl(this.machines, {
					...mapSize,
					zoom: this.isLargeMap ? 12 : 11
				});
				
				console.log('🗺️ 百度地图URL生成成功:', this.mapImageUrl);
			} catch (error) {
				console.error('❌ 生成地图失败:', error);
				uni.showToast({
					title: '地图生成失败',
					icon: 'none'
				});
			}
		},
		
		// 刷新地图
		async refreshMap() {
			uni.showLoading({
				title: '刷新中...'
			});
			
			try {
				await this.loadMachinesAndStats();
				uni.showToast({
					title: '刷新成功',
					icon: 'success'
				});
			} catch (error) {
				console.error('❌ 刷新失败:', error);
				uni.showToast({
					title: '刷新失败',
					icon: 'none'
				});
			} finally {
				uni.hideLoading();
			}
		},
		
		// 切换地图大小
		toggleMapSize() {
			this.isLargeMap = !this.isLargeMap;
			this.generateMap();
		},
		
		// 地图加载成功
		onMapLoad() {
			console.log('🗺️ 地图图片加载成功');
		},
		
		// 地图加载失败
		onMapError(error) {
			console.error('❌ 地图图片加载失败:', error);
			uni.showToast({
				title: '地图加载失败',
				icon: 'none'
			});
		},
		
		// 格式化面积显示
		formatArea(area) {
			if (!area || area === 0) return '0';
			return area.toFixed(1);
		}
	}
}
</script>

<style scoped>
.container {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

/* 地图容器 */
.map-container {
	background: white;
	border-radius: 12rpx;
	margin-bottom: 20rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	position: relative;
}

.baidu-map-container {
	width: 100%;
	height: 600rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #f0f0f0;
}

.baidu-map-image {
	width: 100%;
	height: 100%;
	border-radius: 12rpx;
}

.map-loading {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 100%;
}

.loading-text {
	color: #999;
	font-size: 28rpx;
}

/* 地图控制按钮 */
.map-controls {
	position: absolute;
	top: 20rpx;
	right: 20rpx;
	display: flex;
	flex-direction: column;
	gap: 10rpx;
}

.control-btn {
	background: rgba(255, 255, 255, 0.9);
	padding: 16rpx;
	border-radius: 8rpx;
	display: flex;
	align-items: center;
	gap: 8rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	backdrop-filter: blur(10rpx);
}

.control-btn:active {
	background: rgba(255, 255, 255, 0.7);
}

.btn-text {
	font-size: 24rpx;
	color: #333;
}

.iconfont {
	font-size: 28rpx;
	color: #007AFF;
}

/* 小程序平台占位符 */
.map-placeholder {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	text-align: center;
	z-index: 10;
}

.map-text {
	display: block;
	font-size: 32rpx;
	color: #333;
	margin-bottom: 16rpx;
	font-weight: 500;
}

.map-desc {
	display: block;
	font-size: 28rpx;
	color: #666;
}

/* 统计信息栏 */
.statistics-bar {
	display: flex;
	background: white;
	border-radius: 12rpx;
	padding: 32rpx 20rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.stat-item {
	flex: 1;
	text-align: center;
	position: relative;
}

.stat-item:not(:last-child)::after {
	content: '';
	position: absolute;
	right: 0;
	top: 50%;
	transform: translateY(-50%);
	width: 1rpx;
	height: 60rpx;
	background: #eee;
}

.stat-value {
	display: block;
	font-size: 48rpx;
	font-weight: 600;
	color: #007AFF;
	margin-bottom: 8rpx;
}

.stat-label {
	display: block;
	font-size: 24rpx;
	color: #666;
}

/* 图例栏 */
.legend-bar {
	display: flex;
	background: white;
	border-radius: 12rpx;
	padding: 24rpx;
	justify-content: space-around;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.legend-item {
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.legend-color {
	width: 24rpx;
	height: 24rpx;
	border-radius: 50%;
}

.legend-color.working {
	background-color: #4cd964;
}

.legend-color.idle {
	background-color: #f0ad4e;
}

.legend-color.maintenance {
	background-color: #dd524d;
}

.legend-text {
	font-size: 26rpx;
	color: #333;
}

/* H5端适配 */
/* #ifdef H5 */
.baidu-map-container {
	height: 500rpx;
}

.map-controls {
	top: 15rpx;
	right: 15rpx;
}
/* #endif */

/* 小程序端适配 */
/* #ifndef H5 */
.baidu-map-container {
	height: 400rpx;
}
/* #endif */
</style>
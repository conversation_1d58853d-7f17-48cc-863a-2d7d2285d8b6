// 导入模拟数据（作为备用）
import { mockGetOrderList, mockGetOrderDetail, mockGetWorkHistory } from '../mock/machines.js';

// 配置后端API基础地址（与登录接口保持一致）
const BASE_URL = 'http://192.168.1.106:8080';

// 是否使用模拟数据（开发时可以切换）
const USE_MOCK_DATA = false; // 设置为 false 使用真实接口，true 使用模拟数据

/**
 * 获取作业订单列表API（真实接口）
 * @param {Object} userInfo 用户信息，包含token等
 * @returns {Promise} 返回订单列表
 */
export const getOrderList = (userInfo) => {
	// 如果启用模拟数据，直接返回模拟结果
	if (USE_MOCK_DATA) {
		return mockGetOrderList(userInfo);
	}
	
	return new Promise((resolve, reject) => {
		// 打印请求参数，方便调试
		console.log('获取订单列表 API 请求参数:', {
			token: userInfo.token,
			orderSj: userInfo.username // orderSj实际就是用户的手机号
		});
		
		// 构建正确的URL，将orderSj作为路径参数
		const apiUrl = `${BASE_URL}/fram/order/${userInfo.username}`;
		console.log('请求URL:', apiUrl);
		
		uni.request({
			url: apiUrl, // 使用包含路径参数的URL
			method: 'GET', // 后端确认使用GET请求
			header: {
				'Content-Type': 'application/json',
				'Accept': 'application/json',
				'Authorization': 'Bearer ' + userInfo.token // 添加认证头
			},
			// 只传递token作为查询参数，orderSj已经在URL路径中
			data: {
				token: userInfo.token
			},
			timeout: 10000, // 10秒超时
			success: (res) => {
				console.log('获取订单列表 API 响应:', res);
				
				if (res.statusCode === 200) {
					// 检查后端返回的数据格式
					const responseData = res.data;
					
					// 检查是否是认证失败
					if (responseData && responseData.code === 401) {
						console.error('认证失败，token可能已过期');
						reject({
							code: 401,
							message: responseData.msg || '认证失败，请重新登录'
						});
						return;
					}
					
					// 检查是否是后端路径冲突错误
					if (responseData && responseData.code === 500 && 
						responseData.msg && responseData.msg.includes('Ambiguous handler methods')) {
						console.error('后端接口路径冲突:', responseData.msg);
						reject({
							code: 500,
							message: '后端接口路径冲突，请联系后端开发者修复',
							originalError: responseData.msg
						});
						return;
					}
					
					// 根据后端返回的数据格式进行处理
					if (Array.isArray(responseData)) {
						// 如果后端直接返回订单数组，包装成标准格式
						resolve({
							code: 200,
							message: '获取订单列表成功',
							data: responseData
						});
					} else if (responseData && responseData.code === 200) {
						// 如果后端返回标准格式
						resolve(responseData);
					} else if (responseData && responseData.data && Array.isArray(responseData.data)) {
						// 如果后端返回包含data字段的格式
						resolve({
							code: 200,
							message: responseData.message || '获取订单列表成功',
							data: responseData.data
						});
					} else if (responseData && responseData.success) {
						// 如果后端使用success字段表示成功
						resolve({
							code: 200,
							message: responseData.message || '获取订单列表成功',
							data: responseData.data || responseData.result || []
						});
					} else {
						// 其他情况，使用模拟数据作为备用
						console.warn('后端返回数据格式异常，使用模拟数据');
						console.warn('实际返回数据:', responseData);
						mockGetOrderList(userInfo).then(resolve).catch(reject);
					}
				} else {
					// HTTP错误，使用模拟数据作为备用
					console.warn('HTTP请求失败，状态码:', res.statusCode);
					console.warn('错误信息:', res.data);
					mockGetOrderList(userInfo).then(resolve).catch(reject);
				}
			},
			fail: (err) => {
				console.error('获取订单列表 API 错误:', err);
				// 网络错误，使用模拟数据作为备用
				console.warn('网络请求失败，使用模拟数据');
				mockGetOrderList(userInfo).then(resolve).catch(reject);
			}
		});
	});
};

/**
 * 获取农机列表API（兼容旧接口，实际返回订单列表）
 * @param {Object} userInfo 用户信息
 * @returns {Promise} 返回转换后的机器列表
 */
export const getMachineList = (userInfo) => {
	return getOrderList(userInfo).then(result => {
		if (result.code === 200 && result.data) {
			// 将订单数据转换为旧的机器数据格式，以保持兼容性
			const machines = result.data.map(order => ({
				id: order.id,
				name: order.orderNjname || '未知农机',
				model: order.orderName || '未知作业', // 使用作业名称作为型号
				status: getOrderStatus(order), // 根据订单信息推断状态
				location: parseLocation(order.orderJw), // 解析经纬度
				workArea: parseFloat(order.orderMj) || 0,
				packageCount: order.orderKs || 0,
				driver: {
					name: order.orderSj || '未知',
					phone: order.orderPhone || '未提供' // 如果后端有电话字段
				},
				lastActiveTime: order.orderTime || '未知',
				// 保留原始订单数据
				originalOrder: order
			}));
			
			return {
				...result,
				data: machines
			};
		}
		return result;
	});
};

/**
 * 获取农机列表API（新接口）
 * @param {Object} userInfo 用户信息，包含token等
 * @returns {Promise} 返回农机列表
 */
export const getMachineListNew = (userInfo) => {
	return new Promise((resolve, reject) => {
		console.log('🚜 获取农机列表 API 请求参数:', {
			token: userInfo.token ? '***有token***' : '无token',
			userId: userInfo.userId,
			username: userInfo.username
		});
		
		const apiUrl = `${BASE_URL}/fram/machine/list`;
		console.log('🚜 请求URL:', apiUrl);
		console.log('🚜 查询参数 - 用户名:', userInfo.username);
		
		uni.request({
			url: apiUrl,
			method: 'GET',
			header: {
				'Content-Type': 'application/json',
				'Accept': 'application/json',
				'Authorization': 'Bearer ' + userInfo.token
			},
			data: {
				username: userInfo.username // 使用用户名查询该用户的农机
			},
			timeout: 10000, // 10秒超时
			success: (res) => {
				console.log('🚜 获取农机列表 API 响应:', res);
				
				if (res.statusCode === 200) {
					const responseData = res.data;
					
					// 检查是否是认证失败
					if (responseData && responseData.code === 401) {
						console.error('❌ 认证失败，token可能已过期');
						resolve({
							code: 401,
							message: responseData.msg || '认证失败，请重新登录'
						});
						return;
					}
					
					// 根据后端返回的数据格式进行处理
					if (Array.isArray(responseData)) {
						// 如果后端直接返回农机数组，包装成标准格式
						resolve({
							code: 200,
							message: '获取农机列表成功',
							data: responseData
						});
					} else if (responseData && responseData.code === 200) {
						// 检查是否有rows字段（后端实际返回格式）
						if (responseData.rows && Array.isArray(responseData.rows)) {
							console.log('🚜 找到rows字段，农机数量:', responseData.rows.length);
							// 将农机数据转换为前端需要的格式
							const machines = responseData.rows.map(machine => ({
								// 基本信息
								id: machine.id,
								fname: machine.fname,
								guige: machine.guige,
								owner: machine.owner,
								company: machine.company,
								brand: machine.brand,
								mobile: machine.mobile,
								memo: machine.memo,
								jw: machine.jw,
								
								// 为了兼容现有的显示逻辑，添加一些字段
								name: machine.fname, // 农机名称
								model: machine.guige || '未知型号', // 规格型号
								status: 'idle', // 默认状态为空闲，可以根据实际情况调整
								workArea: 0, // 作业面积，暂时设为0
								packageCount: 0, // 打捆数量，暂时设为0
								driver: {
									name: machine.owner,
									phone: machine.mobile
								},
								lastActiveTime: machine.updateTime || machine.createTime || new Date().toISOString(),
								
								// 保留原始数据
								originalMachine: machine,
								// 为了兼容原来的订单格式，创建一个模拟的订单对象
								originalOrder: {
									id: machine.id,
									orderNjname: machine.fname,
									orderName: '待分配作业',
									orderMj: '0',
									orderTime: machine.updateTime || machine.createTime || new Date().toISOString(),
									orderSj: machine.owner,
									orderJw: machine.jw || '113.087030,28.202149',
									orderKs: 0,
									orderWhere: '待分配作业地点',
									orderMemo: machine.memo || ''
								}
							}));
							
							resolve({
								code: 200,
								message: responseData.msg || '获取农机列表成功',
								data: machines,
								total: responseData.total || machines.length
							});
						} else if (responseData.data && Array.isArray(responseData.data)) {
							// 如果后端返回包含data字段的格式
							resolve({
								code: 200,
								message: responseData.message || responseData.msg || '获取农机列表成功',
								data: responseData.data
							});
						} else {
							// 如果后端返回标准格式但没有rows字段
							resolve(responseData);
						}
					} else if (responseData && responseData.success) {
						// 如果后端使用success字段表示成功
						resolve({
							code: 200,
							message: responseData.message || '获取农机列表成功',
							data: responseData.data || responseData.result || []
						});
					} else {
						// 其他情况，返回错误
						resolve({
							code: responseData?.code || 500,
							message: responseData?.message || responseData?.msg || '获取农机列表失败',
							data: []
						});
					}
				} else {
					// HTTP错误
					resolve({
						code: res.statusCode,
						message: res.data?.message || res.data?.msg || '网络请求失败',
						data: []
					});
				}
			},
			fail: (err) => {
				console.error('🚨 获取农机列表 API 错误:', err);
				resolve({
					code: 500,
					message: '网络连接失败，请检查网络设置',
					data: []
				});
			}
		});
	});
};

/**
 * 获取单个订单详情API
 * @param {String|Number} orderId 订单ID
 * @param {Object} userInfo 用户信息
 * @returns {Promise} 返回订单详情
 */
export const getOrderDetail = (orderId, userInfo) => {
	// 如果启用模拟数据，直接返回模拟结果
	if (USE_MOCK_DATA) {
		return mockGetOrderDetail(orderId, userInfo);
	}
	
	return new Promise((resolve, reject) => {
		console.log('获取订单详情 API 请求参数:', {
			orderId: orderId,
			token: userInfo.token
		});
		
		uni.request({
			url: `${BASE_URL}/getOrderDetail`, // 假设后端接口路径
			method: 'GET',
			header: {
				'Content-Type': 'application/json',
				'Accept': 'application/json',
				'Authorization': 'Bearer ' + userInfo.token
			},
			data: {
				orderId: orderId,
				userId: userInfo.userId
			},
			success: (res) => {
				console.log('获取订单详情 API 响应:', res);
				
				if (res.statusCode === 200) {
					const responseData = res.data;
					
					if (responseData && responseData.code === 200) {
						resolve(responseData);
					} else if (responseData) {
						// 如果直接返回订单对象，包装成标准格式
						resolve({
							code: 200,
							message: '获取订单详情成功',
							data: responseData
						});
					} else {
						// 使用模拟数据作为备用
						mockGetOrderDetail(orderId, userInfo).then(resolve).catch(reject);
					}
				} else {
					// 使用模拟数据作为备用
					mockGetOrderDetail(orderId, userInfo).then(resolve).catch(reject);
				}
			},
			fail: (err) => {
				console.error('获取订单详情 API 错误:', err);
				// 使用模拟数据作为备用
				mockGetOrderDetail(orderId, userInfo).then(resolve).catch(reject);
			}
		});
	});
};

/**
 * 获取单个农机详情API（兼容旧接口）
 * @param {String|Number} machineId 农机ID
 * @param {Object} userInfo 用户信息
 * @returns {Promise} 返回转换后的机器详情
 */
export const getMachineDetail = (machineId, userInfo) => {
	return getOrderDetail(machineId, userInfo).then(result => {
		if (result.code === 200 && result.data) {
			const order = result.data;
			// 转换为旧的机器数据格式
			const machine = {
				id: order.id,
				name: order.orderNjname || '未知农机',
				model: order.orderName || '未知作业',
				status: getOrderStatus(order),
				location: parseLocation(order.orderJw),
				workArea: parseFloat(order.orderMj) || 0,
				packageCount: order.orderKs || 0,
				driver: {
					name: order.orderSj || '未知',
					phone: order.orderPhone || '未提供'
				},
				lastActiveTime: order.orderTime || '未知',
				originalOrder: order
			};
			
			return {
				...result,
				data: machine
			};
		}
		return result;
	});
};

/**
 * 获取工作历史API
 * @param {Object} userInfo 用户信息
 * @param {String|Number} orderId 可选的订单ID
 * @returns {Promise} 返回工作历史
 */
export const getWorkHistory = (userInfo, orderId = null) => {
	// 如果启用模拟数据，直接返回模拟结果
	if (USE_MOCK_DATA) {
		return mockGetWorkHistory(userInfo, orderId);
	}
	
	return new Promise((resolve, reject) => {
		console.log('获取工作历史 API 请求参数:', {
			orderId: orderId,
			token: userInfo.token
		});
		
		uni.request({
			url: `${BASE_URL}/getWorkHistory`, // 假设后端接口路径
			method: 'GET',
			header: {
				'Content-Type': 'application/json',
				'Accept': 'application/json',
				'Authorization': 'Bearer ' + userInfo.token
			},
			data: {
				orderId: orderId,
				userId: userInfo.userId
			},
			success: (res) => {
				console.log('获取工作历史 API 响应:', res);
				
				if (res.statusCode === 200) {
					const responseData = res.data;
					
					if (responseData && responseData.code === 200) {
						resolve(responseData);
					} else if (responseData && Array.isArray(responseData)) {
						resolve({
							code: 200,
							message: '获取工作历史成功',
							data: responseData
						});
					} else {
						// 使用模拟数据作为备用
						mockGetWorkHistory(userInfo, orderId).then(resolve).catch(reject);
					}
				} else {
					// 使用模拟数据作为备用
					mockGetWorkHistory(userInfo, orderId).then(resolve).catch(reject);
				}
			},
			fail: (err) => {
				console.error('获取工作历史 API 错误:', err);
				// 使用模拟数据作为备用
				mockGetWorkHistory(userInfo, orderId).then(resolve).catch(reject);
			}
		});
	});
};

/**
 * 获取统计数据
 * @param {Object} userInfo 用户信息
 * @returns {Promise} 返回统计数据
 */
export const getStatistics = (userInfo) => {
	return new Promise(async (resolve) => {
		try {
			// 先获取订单列表
			const ordersResult = await getOrderList(userInfo);
			const orders = ordersResult.data || [];
			
			// 计算总的工作面积和作业数量
			const totalWorkArea = orders.reduce((sum, order) => sum + (parseFloat(order.orderMj) || 0), 0);
			const totalPackageCount = orders.reduce((sum, order) => sum + (order.orderKs || 0), 0);
			
			// 按订单状态分组统计
			const statusStats = {
				working: 0,
				idle: 0,
				maintenance: 0
			};
			
			orders.forEach(order => {
				const status = getOrderStatus(order);
				if (statusStats[status] !== undefined) {
					statusStats[status]++;
				}
			});
			
			resolve({
				code: 200,
				message: '获取统计数据成功',
				data: {
					totalWorkArea,
					totalPackageCount,
					totalMachines: orders.length,
					statusStats
				}
			});
		} catch (err) {
			console.error('获取统计数据失败:', err);
			resolve({
				code: 500,
				message: '获取统计数据失败',
				data: {
					totalWorkArea: 0,
					totalPackageCount: 0,
					totalMachines: 0,
					statusStats: {
						working: 0,
						idle: 0,
						maintenance: 0
					}
				}
			});
		}
	});
};

// 辅助函数：根据订单信息推断状态
function getOrderStatus(order) {
	if (!order.orderName) return 'idle';
	
	const orderName = order.orderName.toLowerCase();
	if (orderName.includes('维护') || orderName.includes('保养') || orderName.includes('维修')) {
		return 'maintenance';
	} else if (orderName.includes('作业') || orderName.includes('收割') || orderName.includes('播种')) {
		return 'working';
	} else {
		return 'idle';
	}
}

// 辅助函数：解析经纬度字符串
function parseLocation(locationStr) {
	if (!locationStr) return null;
	
	try {
		const coords = locationStr.split(',');
		if (coords.length === 2) {
			return {
				longitude: parseFloat(coords[0].trim()),
				latitude: parseFloat(coords[1].trim())
			};
		}
	} catch (error) {
		console.warn('解析经纬度失败:', locationStr, error);
	}
	
	return null;
}

/**
 * 注册新农机API
 * @param {Object} userInfo 用户信息
 * @param {Object} machineData 农机注册数据
 * @returns {Promise} 返回注册结果
 */
export const registerMachine = (userInfo, machineData) => {
	return new Promise((resolve, reject) => {
		console.log('注册农机 API 请求参数:', {
			token: userInfo.token ? '***有token***' : '无token',
			username: userInfo.username,
			userId: userInfo.userId,
			machineData: machineData
		});

		// 验证必需字段
		const requiredFields = ['fname', 'guige', 'owner', 'company', 'brand', 'mobile'];
		const missingFields = requiredFields.filter(field => !machineData[field]);

		if (missingFields.length > 0) {
			resolve({
				code: 400,
				message: `缺少必需字段: ${missingFields.join(', ')}`,
				data: null
			});
			return;
		}

		if (USE_MOCK_DATA) {
			// 模拟网络延迟
			const delay = Math.random() * 1500 + 800; // 800-2300ms

			setTimeout(() => {
				// 模拟不同的注册结果
				const random = Math.random();

				// 85% 成功率
				if (random < 0.85) {
					const newMachineId = Date.now();

					// 生成一些额外的模拟数据
					const mockExtras = {
						id: newMachineId,
						registerTime: new Date().toISOString(),
						status: 'active',
						lastMaintenance: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
						workingHours: Math.floor(Math.random() * 500 + 100), // 100-600小时
						location: {
							province: '江苏省',
							city: '南京市',
							district: '江宁区'
						}
					};

					resolve({
						code: 200,
						message: '🎉 农机注册成功！欢迎加入智慧农机管理平台',
						data: {
							...machineData,
							...mockExtras
						}
					});

					// 模拟注册成功后的额外提示
					setTimeout(() => {
						console.log('🚜 新农机已添加到您的设备列表');
					}, 500);

				} else if (random < 0.95) {
					// 10% 业务错误（如重复注册等）
					const errorMessages = [
						'该农机编号已存在，请检查后重试',
						'手机号码已被其他农机绑定',
						'农机品牌信息验证失败',
						'所有者信息与系统记录不匹配'
					];

					resolve({
						code: 400,
						message: errorMessages[Math.floor(Math.random() * errorMessages.length)],
						data: null
					});
				} else {
					// 5% 系统错误
					resolve({
						code: 500,
						message: '系统繁忙，请稍后重试',
						data: null
					});
				}
			}, delay);
			return;
		}

		uni.request({
			url: `${BASE_URL}/fram/machine`,
			method: 'POST',
			header: {
				'Content-Type': 'application/json',
				'Accept': 'application/json',
				'Authorization': 'Bearer ' + userInfo.token
			},
			data: {
				...machineData,
				userId: userInfo.userId,
				username: userInfo.username // 添加用户名作为唯一标识符
			},
			success: (res) => {
				console.log('注册农机 API 响应:', res);

				if (res.statusCode === 200) {
					const responseData = res.data;

					if (responseData && responseData.code === 200) {
						resolve(responseData);
					} else {
						resolve({
							code: responseData?.code || 500,
							message: responseData?.message || '注册失败',
							data: null
						});
					}
				} else {
					resolve({
						code: res.statusCode,
						message: '网络请求失败',
						data: null
					});
				}
			},
			fail: (err) => {
				console.error('注册农机 API 错误:', err);
				resolve({
					code: 500,
					message: '网络连接失败',
					data: null
				});
			}
		});
	});
};

/**
 * 修改农机信息API
 * @param {Object} userInfo 用户信息，包含token等
 * @param {String|Number} machineId 农机ID
 * @param {Object} machineData 要修改的农机数据
 * @returns {Promise} 返回修改结果
 */
export const updateMachine = (userInfo, machineId, machineData) => {
	return new Promise((resolve, reject) => {
		console.log('🚜 修改农机信息 API 请求参数:', {
			token: userInfo.token ? '***有token***' : '无token',
			username: userInfo.username,
			machineId: machineId,
			machineData: machineData
		});
		
		// 验证必需字段
		const requiredFields = ['fname', 'guige', 'owner', 'company', 'brand', 'mobile'];
		const missingFields = requiredFields.filter(field => !machineData[field]);
		
		if (missingFields.length > 0) {
			resolve({
				code: 400,
				message: `缺少必需字段: ${missingFields.join(', ')}`,
				data: null
			});
			return;
		}
		
		const apiUrl = `${BASE_URL}/fram/machine`;
		console.log('🚜 修改农机请求URL:', apiUrl);
		
		uni.request({
			url: apiUrl,
			method: 'PUT', // 使用PUT方法进行更新
			header: {
				'Content-Type': 'application/json',
				'Accept': 'application/json',
				'Authorization': 'Bearer ' + userInfo.token
			},
			data: {
				id: machineId, // 将农机ID包含在请求体中
				...machineData,
				username: userInfo.username // 添加用户名进行权限验证
			},
			timeout: 10000,
			success: (res) => {
				console.log('🚜 修改农机信息 API 响应:', res);
				
				if (res.statusCode === 200) {
					const responseData = res.data;
					
					if (responseData && responseData.code === 200) {
						resolve({
							code: 200,
							message: responseData.msg || responseData.message || '修改成功',
							data: responseData.data || machineData
						});
					} else {
						resolve({
							code: responseData?.code || 500,
							message: responseData?.msg || responseData?.message || '修改失败',
							data: null
						});
					}
				} else {
					resolve({
						code: res.statusCode,
						message: res.data?.msg || res.data?.message || '网络请求失败',
						data: null
					});
				}
			},
			fail: (err) => {
				console.error('🚨 修改农机信息 API 错误:', err);
				resolve({
					code: 500,
					message: '网络连接失败，请检查网络设置',
					data: null
				});
			}
		});
	});
};

/**
 * 删除农机API
 * @param {Object} userInfo 用户信息，包含token等
 * @param {String|Number} machineId 农机ID
 * @returns {Promise} 返回删除结果
 */
export const deleteMachine = (userInfo, machineId) => {
	return new Promise((resolve, reject) => {
		console.log('🚜 删除农机 API 请求参数:', {
			token: userInfo.token ? '***有token***' : '无token',
			username: userInfo.username,
			machineId: machineId
		});
		
		const apiUrl = `${BASE_URL}/fram/machine/${machineId}`;
		console.log('🚜 删除农机请求URL:', apiUrl);
		
		uni.request({
			url: apiUrl,
			method: 'DELETE',
			header: {
				'Content-Type': 'application/json',
				'Accept': 'application/json',
				'Authorization': 'Bearer ' + userInfo.token
			},
			data: {
				username: userInfo.username // 添加用户名进行权限验证
			},
			timeout: 10000,
			success: (res) => {
				console.log('🚜 删除农机 API 响应:', res);
				
				if (res.statusCode === 200) {
					const responseData = res.data;
					
					if (responseData && responseData.code === 200) {
						resolve({
							code: 200,
							message: responseData.msg || responseData.message || '删除成功',
							data: responseData.data
						});
					} else {
						resolve({
							code: responseData?.code || 500,
							message: responseData?.msg || responseData?.message || '删除失败',
							data: null
						});
					}
				} else {
					resolve({
						code: res.statusCode,
						message: res.data?.msg || res.data?.message || '网络请求失败',
						data: null
					});
				}
			},
			fail: (err) => {
				console.error('🚨 删除农机 API 错误:', err);
				resolve({
					code: 500,
					message: '网络连接失败，请检查网络设置',
					data: null
				});
			}
		});
	});
};

/**
 * 创建新作业API
 * @param {Object} userInfo 用户信息
 * @param {String|Number} machineId 农机ID
 * @param {Object} jobData 作业数据
 * @returns {Promise} 返回创建结果
 */
export const createJob = (userInfo, machineId, jobData) => {
	return new Promise((resolve, reject) => {
		console.log('🚜 创建作业 API 请求参数:', {
			token: userInfo.token ? '***有token***' : '无token',
			username: userInfo.username,
			userId: userInfo.userId,
			machineId: machineId,
			jobData: jobData
		});



		uni.request({
			url: `${BASE_URL}/fram/order`,
			method: 'POST',
			header: {
				'Content-Type': 'application/json',
				'Accept': 'application/json',
				'Authorization': 'Bearer ' + userInfo.token
			},
			data: {
				machineId: machineId,
				userId: userInfo.userId,
				username: userInfo.username, // 添加用户名进行权限验证
				...jobData // 展开作业数据，包含orderNjname, orderName, orderWhere, orderJw等字段
			},
			success: (res) => {
				console.log('🚜 创建作业 API 响应:', res);

				if (res.statusCode === 200) {
					const responseData = res.data;

					if (responseData && responseData.code === 200) {
						// 标准化响应格式，确保有data字段
						resolve({
							code: responseData.code,
							message: responseData.msg || responseData.message || '操作成功',
							data: responseData.data || responseData // 如果没有data字段，将整个响应作为data
						});
					} else {
						resolve({
							code: responseData?.code || 500,
							message: responseData?.msg || responseData?.message || '创建作业失败',
							data: null
						});
					}
				} else {
					resolve({
						code: res.statusCode,
						message: '网络请求失败',
						data: null
					});
				}
			},
			fail: (err) => {
				console.error('❌ 创建作业 API 错误:', err);
				resolve({
					code: 500,
					message: '网络连接失败',
					data: null
				});
			}
		});
	});
};

/**
 * 开始作业API
 * @param {Object} userInfo 用户信息
 * @param {String|Number} jobId 作业ID
 * @returns {Promise} 返回开始结果
 */
export const startJob = (userInfo, jobId) => {
	return new Promise((resolve, reject) => {
		console.log('🚀 开始作业 API 请求参数:', {
			token: userInfo.token ? '***有token***' : '无token',
			username: userInfo.username,
			userId: userInfo.userId,
			jobId: jobId
		});



		uni.request({
			url: `${BASE_URL}/fram/order`,
			method: 'POST',
			header: {
				'Content-Type': 'application/json',
				'Accept': 'application/json',
				'Authorization': 'Bearer ' + userInfo.token
			},
			data: {
				jobId: jobId,
				userId: userInfo.userId,
				username: userInfo.username, // 添加用户名进行权限验证
				action: "start" // 发送start指令
			},
			success: (res) => {
				console.log('🚀 开始作业 API 响应:', res);

				if (res.statusCode === 200) {
					const responseData = res.data;

					if (responseData && responseData.code === 200) {
						resolve(responseData);
					} else {
						resolve({
							code: responseData?.code || 500,
							message: responseData?.message || '开始作业失败',
							data: null
						});
					}
				} else {
					resolve({
						code: res.statusCode,
						message: '网络请求失败',
						data: null
					});
				}
			},
			fail: (err) => {
				console.error('❌ 开始作业 API 错误:', err);
				resolve({
					code: 500,
					message: '网络连接失败',
					data: null
				});
			}
		});
	});
};

/**
 * 暂停作业API
 * @param {Object} userInfo 用户信息
 * @param {String|Number} jobId 作业ID
 * @returns {Promise} 返回暂停结果
 */
export const pauseJob = (userInfo, jobId) => {
	return new Promise((resolve, reject) => {
		console.log('🚜 暂停作业 API 请求参数:', {
			token: userInfo.token ? '***有token***' : '无token',
			username: userInfo.username,
			userId: userInfo.userId,
			jobId: jobId
		});

		if (USE_MOCK_DATA) {
			// 模拟暂停成功
			setTimeout(() => {
				resolve({
					code: 200,
					message: '作业暂停成功',
					data: {
						jobId: jobId,
						status: 'paused',
						pauseTime: new Date().toISOString()
					}
				});
			}, 300);
			return;
		}

		uni.request({
			url: `${BASE_URL}/fram/order`,
			method: 'POST',
			header: {
				'Content-Type': 'application/json',
				'Accept': 'application/json',
				'Authorization': 'Bearer ' + userInfo.token
			},
			data: {
				jobId: jobId,
				userId: userInfo.userId,
				username: userInfo.username, // 添加用户名进行权限验证
				action: "pause" // 暂停作业指令
			},
			success: (res) => {
				console.log('🚜 暂停作业 API 响应:', res);

				if (res.statusCode === 200) {
					const responseData = res.data;

					if (responseData && responseData.code === 200) {
						resolve(responseData);
					} else {
						resolve({
							code: responseData?.code || 500,
							message: responseData?.message || '暂停作业失败',
							data: null
						});
					}
				} else {
					resolve({
						code: res.statusCode,
						message: '网络请求失败',
						data: null
					});
				}
			},
			fail: (err) => {
				console.error('暂停作业 API 错误:', err);
				resolve({
					code: 500,
					message: '网络连接失败',
					data: null
				});
			}
		});
	});
};

/**
 * 停止作业API
 * @param {Object} userInfo 用户信息
 * @param {String|Number} jobId 作业ID
 * @returns {Promise} 返回停止结果
 */
export const stopJob = (userInfo, jobId) => {
	return new Promise((resolve, reject) => {
		console.log('🚜 停止作业 API 请求参数:', {
			token: userInfo.token ? '***有token***' : '无token',
			username: userInfo.username,
			userId: userInfo.userId,
			jobId: jobId
		});

		if (USE_MOCK_DATA) {
			// 模拟停止成功，返回作业面积
			setTimeout(() => {
				const mockWorkArea = (Math.random() * 100 + 50).toFixed(1); // 随机生成50-150亩的作业面积
				resolve({
					code: 200,
					message: '作业停止成功',
					data: {
						jobId: jobId,
						status: 'completed',
						endTime: new Date().toISOString(),
						workArea: parseFloat(mockWorkArea),
						packageCount: Math.floor(Math.random() * 200 + 100) // 随机生成100-300个打捆数
					}
				});
			}, 500);
			return;
		}

		uni.request({
			url: `${BASE_URL}/fram/order`,
			method: 'POST',
			header: {
				'Content-Type': 'application/json',
				'Accept': 'application/json',
				'Authorization': 'Bearer ' + userInfo.token
			},
			data: {
				jobId: jobId,
				userId: userInfo.userId,
				username: userInfo.username, // 添加用户名进行权限验证
				action: "stop" // 停止作业指令
			},
			success: (res) => {
				console.log('🚜 停止作业 API 响应:', res);

				if (res.statusCode === 200) {
					const responseData = res.data;

					if (responseData && responseData.code === 200) {
						resolve(responseData);
					} else {
						resolve({
							code: responseData?.code || 500,
							message: responseData?.message || '停止作业失败',
							data: null
						});
					}
				} else {
					resolve({
						code: res.statusCode,
						message: '网络请求失败',
						data: null
					});
				}
			},
			fail: (err) => {
				console.error('停止作业 API 错误:', err);
				resolve({
					code: 500,
					message: '网络连接失败',
					data: null
				});
			}
		});
	});
};

/**
 * 获取实时作业数据API
 * @param {Object} userInfo 用户信息
 * @param {String|Number} jobId 作业ID
 * @returns {Promise} 返回实时数据
 */
export const getJobRealTimeData = (userInfo, jobId) => {
	return new Promise((resolve, reject) => {
		console.log('📊 获取实时作业数据 API 请求参数:', {
			token: userInfo.token ? '***有token***' : '无token',
			username: userInfo.username,
			userId: userInfo.userId,
			jobId: jobId
		});



		uni.request({
			url: `${BASE_URL}/fram/order/realtime`,
			method: 'GET',
			header: {
				'Content-Type': 'application/json',
				'Accept': 'application/json',
				'Authorization': 'Bearer ' + userInfo.token
			},
			data: {
				jobId: jobId,
				userId: userInfo.userId,
				username: userInfo.username // 添加用户名进行权限验证
			},
			success: (res) => {
				console.log('📊 获取实时作业数据 API 响应:', res);

				if (res.statusCode === 200) {
					const responseData = res.data;

					if (responseData && responseData.code === 200) {
						resolve(responseData);
					} else {
						resolve({
							code: responseData?.code || 500,
							message: responseData?.message || '获取实时数据失败',
							data: null
						});
					}
				} else {
					resolve({
						code: res.statusCode,
						message: '网络请求失败',
						data: null
					});
				}
			},
			fail: (err) => {
				console.error('❌ 获取实时作业数据 API 错误:', err);
				resolve({
					code: 500,
					message: '网络连接失败',
					data: null
				});
			}
		});
	});
};

/**
 * 删除作业API
 * @param {Object} userInfo 用户信息
 * @param {String|Number} jobId 作业ID
 * @returns {Promise} 返回删除结果
 */
export const deleteJob = (userInfo, jobId) => {
	return new Promise((resolve, reject) => {
		console.log('删除作业 API 请求参数:', {
			token: userInfo.token,
			jobId: jobId
		});

		if (USE_MOCK_DATA) {
			// 模拟删除成功
			setTimeout(() => {
				resolve({
					code: 200,
					message: '作业删除成功',
					data: {
						jobId: jobId,
						deleted: true
					}
				});
			}, 500);
			return;
		}

		uni.request({
			url: `${BASE_URL}/job/delete`,
			method: 'DELETE',
			header: {
				'Content-Type': 'application/json',
				'Accept': 'application/json',
				'Authorization': 'Bearer ' + userInfo.token
			},
			data: {
				jobId: jobId,
				userId: userInfo.userId
			},
			success: (res) => {
				console.log('删除作业 API 响应:', res);

				if (res.statusCode === 200) {
					const responseData = res.data;

					if (responseData && responseData.code === 200) {
						resolve(responseData);
					} else {
						resolve({
							code: responseData?.code || 500,
							message: responseData?.message || '删除作业失败',
							data: null
						});
					}
				} else {
					resolve({
						code: res.statusCode,
						message: '网络请求失败',
						data: null
					});
				}
			},
			fail: (err) => {
				console.error('删除作业 API 错误:', err);
				resolve({
					code: 500,
					message: '网络连接失败',
					data: null
				});
			}
		});
	});
};

// 导出配置，方便调试时切换
export const API_CONFIG = {
	BASE_URL,
	USE_MOCK_DATA
};
# 农机作业进度功能测试指南

## 功能概述

本次更新完善了农机作业进度页面（job-progress）的多项重要功能：

1. **实时分钟秒计时**：显示精确到秒的作业运行时长，前端自动计时
2. **作业详细信息展示**：显示农机名称、作业名称、作业时间、司机、地点等信息
3. **完善的暂停/停止功能**：与后端API接口联调，包含完整的错误处理
4. **界面美化**：符合整体设计风格的现代化界面

## 后端接口信息

### 1. 暂停作业接口
- **请求地址**：`POST /job/pause`
- **请求头**：`Authorization: Bearer {token}`
- **请求参数**：
  ```json
  {
    "jobId": "作业ID",
    "userId": "用户ID",
    "username": "用户名"
  }
  ```
- **响应格式**：
  ```json
  {
    "code": 200,
    "message": "作业暂停成功",
    "data": {
      "jobId": "作业ID",
      "status": "paused",
      "pauseTime": "2024-01-30T10:30:00.000Z"
    }
  }
  ```

### 2. 停止作业接口
- **请求地址**：`POST /job/stop`
- **请求头**：`Authorization: Bearer {token}`
- **请求参数**：
  ```json
  {
    "jobId": "作业ID",
    "userId": "用户ID", 
    "username": "用户名"
  }
  ```
- **响应格式**：
  ```json
  {
    "code": 200,
    "message": "作业停止成功",
    "data": {
      "jobId": "作业ID",
      "status": "completed",
      "endTime": "2024-01-30T10:30:00.000Z",
      "workArea": 120.5,
      "packageCount": 200
    }
  }
  ```

## 作业详细信息字段说明

根据后端提供的字段结构，页面会显示以下信息：

| 字段名 | 说明 | 显示位置 |
|--------|------|----------|
| `orderNjname` | 作业农机名称 | 作业详情卡片 - 作业农机 |
| `orderName` | 作业名称 | 作业详情卡片 - 作业名称 |
| `orderTime` | 作业时间（年月日） | 作业详情卡片 - 作业日期 |
| `orderSj` | 作业司机 | 作业详情卡片 - 作业司机 |
| `orderWhere` | 作业地点 | 作业详情卡片 - 作业地点 |
| `orderMemo` | 作业备注 | 作业详情卡片 - 备注信息 |
| `orderMj` | 作业面积 | （暂未显示，可后续添加） |
| `orderJw` | 作业经纬度 | （暂未显示，可用于地图定位） |
| `orderKs` | 作业数量 | （暂未显示，可后续添加） |

## 测试场景

### 场景1：实时计时功能测试
1. **进入作业进度页面**
   - 从农机详情页面点击"开始新作业"
   - 或直接访问作业进度页面

2. **计时显示测试**
   - 确认页面显示"X分XX秒"格式的运行时长
   - 等待几秒，确认时间每秒自动更新
   - 切换到其他页面再回来，确认计时继续

3. **状态切换时的计时测试**
   - 点击"暂停作业"，确认计时停止
   - 点击"继续作业"，确认计时恢复
   - 点击"停止作业"，确认最终时长正确

### 场景2：作业详细信息展示测试
1. **信息卡片显示**
   - 确认"作业详情"卡片正确显示
   - 验证所有字段都有相应的图标和标签
   - 检查信息布局是否美观

2. **信息内容验证**
   - **作业农机**：应显示当前农机名称
   - **作业名称**：应显示作业名称或默认值
   - **作业日期**：应显示YYYY-MM-DD格式的日期
   - **作业司机**：应显示司机姓名或"待分配"
   - **作业地点**：应显示地点信息或"待分配地点"
   - **备注信息**：如有备注则显示，无则隐藏此项

3. **模拟模式测试**
   - 在模拟模式下，确认显示模拟数据
   - 模拟数据应包含所有字段的示例值

### 场景3：暂停/继续作业功能测试
1. **暂停作业测试**
   - 在作业运行时点击"暂停作业"按钮
   - 确认按钮显示加载状态
   - 成功后应显示"作业已暂停"提示
   - 按钮文字应变为"继续作业"
   - 状态指示器应变为暂停状态

2. **继续作业测试**
   - 在暂停状态下点击"继续作业"按钮
   - 确认按钮显示加载状态
   - 成功后应显示"作业已继续"提示
   - 按钮文字应变为"暂停作业"
   - 状态指示器应变为运行状态

3. **网络错误处理**
   - 测试网络连接断开时的错误处理
   - 测试token过期时的认证失败处理
   - 确认错误提示信息准确友好

### 场景4：停止作业功能测试
1. **停止确认弹窗**
   - 点击"停止作业"按钮
   - 确认弹出确认对话框
   - 对话框应显示警告文字和确认按钮

2. **取消停止**
   - 在确认对话框中点击"取消"
   - 确认对话框关闭，返回作业进度页面
   - 作业状态应保持不变

3. **确认停止**
   - 在确认对话框中点击"确认停止"
   - 确认按钮显示"停止中..."加载状态
   - 成功后应弹出作业完成结果弹窗
   - 结果弹窗应显示作业统计数据

4. **作业完成结果验证**
   - 确认显示作业面积、打捆数量等统计
   - 确认显示作业时长、效率等指标
   - 确认显示质量评分和建议
   - 点击"返回详情"应回到农机详情页面

### 场景5：错误处理测试
1. **认证失败测试（401）**
   - 在token过期情况下执行操作
   - 应弹出"认证失败"对话框
   - 点击"重新登录"应跳转到登录页面

2. **网络错误测试**
   - 在网络断开情况下执行操作
   - 应显示"网络连接失败"等友好提示
   - 用户可以重新尝试操作

3. **服务器错误测试**
   - 模拟后端返回错误状态码
   - 应显示相应的错误信息
   - 不应导致页面崩溃

## 调试信息说明

### 暂停作业调试信息
- `🚜 暂停作业 API 请求参数: {token: "***有token***", username: "...", userId: "...", jobId: "..."}`
- `暂停作业 API 响应: {statusCode: 200, data: {...}}`

### 停止作业调试信息  
- `🚜 停止作业 API 请求参数: {token: "***有token***", username: "...", userId: "...", jobId: "..."}`
- `停止作业 API 响应: {statusCode: 200, data: {...}}`

### 计时相关调试信息
- 每秒更新时间显示，可在控制台观察计算过程
- 暂停/继续时会有相应的定时器启动/停止日志

## 界面元素说明

### 1. 状态指示器
- **运行中**：绿色脉冲动画，显示🚜图标
- **已暂停**：橙色静态状态，显示⏸️图标  
- **已完成**：蓝色状态，显示✅图标

### 2. 作业详情卡片
- **蓝色主题**：与系统整体风格一致
- **网格布局**：信息排列整齐美观
- **图标标识**：每个信息项都有相应图标
- **重点突出**：农机名称和地点信息特别突出显示

### 3. 实时数据卡片
- **渐变背景**：现代化视觉效果
- **数据指标**：打捆数量、作业时长等
- **自动刷新**：30秒自动更新一次数据

### 4. 控制按钮
- **动态文字**：根据状态显示不同文字
- **加载状态**：操作时显示加载动画
- **颜色编码**：暂停（橙色）、继续（绿色）、停止（红色）

## 常见问题排查

### 1. 计时不更新
**原因**：可能是定时器被清理或页面未正确初始化
**解决**：
- 检查控制台是否有JavaScript错误
- 确认页面onShow时定时器正确启动
- 重新进入页面重置定时器

### 2. 作业信息显示不全
**原因**：可能是数据传递有问题或字段映射错误
**解决**：
- 检查传入页面的参数是否完整
- 确认jobInfo对象是否正确初始化
- 在模拟模式下测试基本显示

### 3. 暂停/停止功能无响应
**原因**：可能是API接口未正确配置或token无效
**解决**：
- 检查控制台API调用日志
- 确认BASE_URL配置正确
- 验证用户登录状态和token有效性

### 4. 页面样式异常
**原因**：可能是CSS冲突或设备兼容性问题
**解决**：
- 在不同设备和浏览器测试
- 检查CSS选择器是否正确
- 确认使用标准的uni-app样式写法

### 5. 错误处理不生效
**原因**：可能是try-catch块未正确覆盖或错误码判断有误
**解决**：
- 检查API返回的实际数据格式
- 确认错误码判断逻辑正确
- 测试各种错误场景的处理

## 成功标志

### 实时计时功能
✅ 显示"X分XX秒"格式的时间  
✅ 每秒自动更新时间显示  
✅ 暂停时停止计时，继续时恢复计时  
✅ 停止时显示最终作业时长  

### 作业详细信息
✅ 作业详情卡片正确显示  
✅ 所有字段都有适当的图标和样式  
✅ 信息布局美观，符合设计风格  
✅ 模拟模式和实际模式都能正常显示  

### 暂停/停止功能
✅ 暂停/继续功能正常工作  
✅ 停止作业功能正常工作  
✅ 所有操作都有适当的加载状态  
✅ 错误情况得到正确处理  

### 界面美化
✅ 整体视觉效果现代化美观  
✅ 颜色搭配协调一致  
✅ 动画效果流畅自然  
✅ 响应式布局适配良好  

## 注意事项

1. **定时器管理**：页面切换时注意清理定时器，避免内存泄漏

2. **数据同步**：确保前端计时与后端记录的时间保持一致

3. **错误恢复**：网络错误后应能正常恢复功能，不影响用户体验

4. **性能优化**：长时间作业时注意内存使用和性能表现

5. **用户体验**：所有操作都应有明确的反馈，避免用户困惑

通过以上全面测试，可以确保农机作业进度功能稳定可靠，为用户提供优质的作业监控体验。
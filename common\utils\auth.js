// 认证相关的工具函数

/**
 * 保存用户登录信息到本地
 * @param {Object} userInfo - 用户信息对象
 */
export const saveUserInfo = (userInfo) => {
	console.log('Saving user info:', userInfo);
	
	// 如果提供了userInfo对象，并且有token
	if (userInfo && userInfo.token) {
		// 确定用户角色
		let role = 'user';
		
		// 根据roleType或code确定角色
		if (userInfo.roleType === 'admin') {
			role = 'admin';
		} else if (userInfo.code === 1) {
			role = 'admin';
		}
		
		const formattedUserInfo = {
			// 用户基本信息 - 允许使用生成的userId
			userId: userInfo.userId || userInfo.user_id || userInfo.id || 'user_' + (userInfo.username || Date.now()),
			username: userInfo.username || userInfo.userName || userInfo.name || '',
			role: role, // 根据roleType或code值确定角色
			// 使用真实token
			token: userInfo.token,
			// 保存code值或根据角色生成
			code: userInfo.code || (role === 'admin' ? 1 : 2),
			// 保存roleType
			roleType: userInfo.roleType || role,
			// 其他可能的字段
			accessMachines: userInfo.accessMachines || userInfo.access_machines || userInfo.permissions || [],
			// 保存原始数据，以防需要
			originalData: userInfo
		};
		
		// 确保有token
		if (!formattedUserInfo.token) {
			console.error('缺少token，无法保存用户信息');
			return false;
		}
		
		console.log('Formatted user info:', formattedUserInfo);
		uni.setStorageSync('userInfo', formattedUserInfo);
		return true;
	}
	
	console.error('无效的用户信息或缺少token，无法保存');
	return false;
};

/**
 * 获取本地存储的用户登录信息
 * @returns {Object|null} - 存储的用户信息，不存在则返回null
 */
export const getUserInfo = () => {
	try {
		const userInfo = uni.getStorageSync('userInfo');
		if (userInfo && userInfo.token) {
			return userInfo;
		}
		return null;
	} catch (e) {
		return null;
	}
};

/**
 * 清除本地存储的用户登录信息
 */
export const clearUserInfo = () => {
	uni.removeStorageSync('userInfo');
};

/**
 * 检查用户是否有权限访问某个农机
 * @param {Object} userInfo - 用户信息
 * @param {String} machineId - 农机ID
 * @returns {Boolean} - 是否有权限
 */
export const checkMachineAccess = (userInfo, machineId) => {
	if (!userInfo) {
		return false;
	}
	
	// 管理员或者accessMachines为'all'的用户可以访问所有农机
	if (userInfo.role === 'admin' || userInfo.accessMachines === 'all') {
		return true;
	}
	
	// 检查用户的可访问农机列表中是否包含该农机
	return Array.isArray(userInfo.accessMachines) && userInfo.accessMachines.includes(machineId);
};

/**
 * 检查用户是否为管理员
 * @param {Object} userInfo - 用户信息
 * @returns {Boolean} - 是否为管理员
 */
export const isAdmin = (userInfo) => {
	return userInfo && userInfo.role === 'admin';
}; 
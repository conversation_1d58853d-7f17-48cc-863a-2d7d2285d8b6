import App from './App'

// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'
Vue.config.productionTip = false
App.mpType = 'app'

// 导入工具函数
import { getUserInfo } from './common/utils/auth.js'

// 添加全局导航守卫
const whiteList = ['/pages/login/login'] // 不需要登录的页面

// 页面跳转前检查登录状态
uni.addInterceptor('navigateTo', {
  invoke(params) {
    checkLoginStatus(params)
    return params
  }
})

uni.addInterceptor('switchTab', {
  invoke(params) {
    checkLoginStatus(params)
    return params
  }
})

uni.addInterceptor('reLaunch', {
  invoke(params) {
    checkLoginStatus(params)
    return params
  }
})

// 检查登录状态
function checkLoginStatus(params) {
  const url = params.url.split('?')[0]
  // 不在白名单中的页面，需要登录
  if (whiteList.indexOf(url) === -1) {
    const userInfo = getUserInfo()
    if (!userInfo) {
      uni.showToast({
        title: '请先登录',
        icon: 'none'
      })
      // 跳转到登录页
      setTimeout(() => {
        uni.reLaunch({
          url: '/pages/login/login'
        })
      }, 1500)
      return false
    }
  }
  return true
}

const app = new Vue({
  ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
export function createApp() {
  const app = createSSRApp(App)
  return {
    app
  }
}
// #endif
// 百度地图配置
export default {
    // 百度地图API密钥
    ak: 'rjlB4osFTDoQtCSkvpZ7HxftinyRcI62',
    
    // 地图默认配置
    defaultMapOptions: {
        zoom: 11, // 初始缩放级别
        center: '113.087030,28.202149', // 默认地图中心点（经度，纬度格式）
        width: 400,
        height: 300,
        coordtype: 'bd09ll' // 百度经纬度坐标系
    },
    
    // 百度地图API服务地址
    services: {
        staticImage: 'https://api.map.baidu.com/staticimage/v2',
        ipLocation: 'https://api.map.baidu.com/location/ip'
    },
    
    // 标记样式配置（百度地图标记样式）
    markerStyles: {
        working: {
            size: 'l',
            label: 'W', 
            color: '0x4cd964' // 绿色 - 作业中
        },
        idle: {
            size: 'm',
            label: 'I',
            color: '0xf0ad4e' // 橙色 - 空闲
        },
        maintenance: {
            size: 'm',
            label: 'M',
            color: '0xdd524d' // 红色 - 维护中
        },
        default: {
            size: 's',
            label: 'A',
            color: '0x007AFF' // 蓝色 - 默认
        }
    }
}; 
import mapConfig from '@/common/config/map.js';

/**
 * 百度地图工具类
 * 提供百度地图相关的工具函数，包括静态图生成、IP定位、标记点管理等
 */

/**
 * 百度IP定位 - 获取当前IP的大致位置信息
 * 注意：此API仅适用于服务端调用，浏览器端会遇到CORS跨域问题
 * 建议在实际项目中通过后端代理调用或使用其他定位方案
 * @param {string} ip - 可选，指定IP地址，不传则使用当前请求IP
 * @returns {Promise<Object>} 位置信息
 */
export async function getBaiduLocationByIP(ip = '') {
    return new Promise((resolve, reject) => {
		console.log('🌍 开始百度IP定位...');
		console.warn('⚠️ 注意：百度IP定位API不支持浏览器直接调用，存在CORS限制');
		
		// 构建请求URL
		let url = `${mapConfig.services.ipLocation}?ak=${mapConfig.ak}&coor=bd09ll`;
		if (ip) {
			url += `&ip=${ip}`;
		}
		
		uni.request({
			url: url,
			method: 'GET',
			success: (res) => {
				console.log('🌍 百度IP定位响应:', res);
				
				if (res.statusCode === 200 && res.data) {
					const data = res.data;
					
					if (data.status === 0) {
						// 定位成功
						const locationInfo = {
							lng: parseFloat(data.content.point.x),
							lat: parseFloat(data.content.point.y),
							formatted_address: data.content.address,
							address: data.content.address,
							province: data.content.address_detail.province,
							city: data.content.address_detail.city,
							district: data.content.address_detail.district,
							street: data.content.address_detail.street,
							adcode: data.content.address_detail.adcode
						};
						
						console.log('🌍 百度IP定位成功:', locationInfo);
						resolve(locationInfo);
					} else {
						console.error('🌍 百度IP定位失败:', data);
						reject(new Error(`定位失败，状态码: ${data.status}`));
					}
            } else {
					console.error('🌍 百度IP定位请求失败:', res);
					reject(new Error('网络请求失败'));
				}
			},
			fail: (err) => {
				console.error('🌍 百度IP定位网络错误:', err);
				console.error('💡 建议：使用uni.getLocation进行设备GPS定位');
				reject(new Error('CORS跨域限制，建议使用GPS定位'));
			}
		});
	});
}

/**
 * 简单的默认位置获取（当GPS定位失败时使用）
 * @returns {Object} 默认位置信息
 */
export function getDefaultLocation() {
	const defaultCoords = mapConfig.defaultMapOptions.center.split(',');
	return {
		lng: parseFloat(defaultCoords[0]),
		lat: parseFloat(defaultCoords[1]),
		formatted_address: '默认位置',
		address: '默认位置 - 请手动输入具体地址'
	};
}

/**
 * 生成百度静态地图URL
 * @param {Object} options - 地图配置选项
 * @param {string} options.center - 地图中心点 "lng,lat"
 * @param {number} options.zoom - 缩放级别 (3-18)
 * @param {number} options.width - 图片宽度 (1-1024)
 * @param {number} options.height - 图片高度 (1-1024)
 * @param {Array} options.markers - 标记点数组
 * @param {Array} options.markerStyles - 标记样式数组
 * @returns {string} 百度静态地图URL
 */
export function generateBaiduStaticMapUrl(options = {}) {
	const {
		center = mapConfig.defaultMapOptions.center,
		zoom = mapConfig.defaultMapOptions.zoom,
		width = mapConfig.defaultMapOptions.width,
		height = mapConfig.defaultMapOptions.height,
		markers = [],
		markerStyles = [],
		scaler = 2 // 高清图
	} = options;
	
	let url = `${mapConfig.services.staticImage}?ak=${mapConfig.ak}`;
	url += `&center=${center}`;
	url += `&zoom=${zoom}`;
	url += `&width=${width}`;
	url += `&height=${height}`;
	url += `&scaler=${scaler}`;
	url += `&coordtype=${mapConfig.defaultMapOptions.coordtype}`;
	
	// 添加标记点
	if (markers && markers.length > 0) {
		const markersStr = markers.join('|');
		url += `&markers=${markersStr}`;
		
		// 添加标记样式
		if (markerStyles && markerStyles.length > 0) {
			const stylesStr = markerStyles.join('|');
			url += `&markerStyles=${stylesStr}`;
		}
	}
	
	console.log('🗺️ 生成百度静态地图URL:', url);
	return url;
}

/**
 * 为农机生成地图标记点
 * @param {Array} machines - 农机数据数组
 * @returns {Object} 包含markers和markerStyles的对象
 */
export function generateMachineMarkers(machines = []) {
	const markers = [];
	const markerStyles = [];
	
	machines.forEach((machine, index) => {
		// 检查农机是否有有效的经纬度
		if (machine.jw && machine.jw.includes(',')) {
			const [lng, lat] = machine.jw.split(',');
			if (lng && lat && !isNaN(lng) && !isNaN(lat)) {
				markers.push(`${lng},${lat}`);
				
				// 根据农机状态选择标记样式
				const status = machine.status || 'idle';
				const styleConfig = mapConfig.markerStyles[status] || mapConfig.markerStyles.default;
				const styleStr = `${styleConfig.size},${styleConfig.label},${styleConfig.color}`;
				markerStyles.push(styleStr);
			}
		}
	});
	
	console.log('🚜 生成农机标记点:', { markers, markerStyles });
	return { markers, markerStyles };
}

/**
 * 生成农机分布地图URL
 * @param {Array} machines - 农机数据数组
 * @param {Object} mapOptions - 地图配置选项
 * @returns {string} 包含农机标记的静态地图URL
 */
export function generateMachinesMapUrl(machines = [], mapOptions = {}) {
	const { markers, markerStyles } = generateMachineMarkers(machines);
	
	// 如果有农机位置数据，计算地图中心点
	let center = mapOptions.center || mapConfig.defaultMapOptions.center;
	if (markers.length > 0) {
		center = calculateMapCenter(markers);
	}
	
	const options = {
		center,
		zoom: mapOptions.zoom || mapConfig.defaultMapOptions.zoom,
		width: mapOptions.width || mapConfig.defaultMapOptions.width,
		height: mapOptions.height || mapConfig.defaultMapOptions.height,
		markers,
		markerStyles
	};
	
	return generateBaiduStaticMapUrl(options);
}

/**
 * 计算多个坐标点的中心点
 * @param {Array} coordinates - 坐标数组 ["lng,lat", "lng,lat", ...]
 * @returns {string} 中心点坐标 "lng,lat"
 */
export function calculateMapCenter(coordinates = []) {
	if (coordinates.length === 0) {
		return mapConfig.defaultMapOptions.center;
	}
	
	if (coordinates.length === 1) {
		return coordinates[0];
	}
	
	let totalLng = 0;
	let totalLat = 0;
	let validCount = 0;
	
	coordinates.forEach(coord => {
		const [lng, lat] = coord.split(',');
		if (lng && lat && !isNaN(lng) && !isNaN(lat)) {
			totalLng += parseFloat(lng);
			totalLat += parseFloat(lat);
			validCount++;
		}
	});
	
	if (validCount === 0) {
		return mapConfig.defaultMapOptions.center;
	}
	
	const centerLng = (totalLng / validCount).toFixed(6);
	const centerLat = (totalLat / validCount).toFixed(6);
	
	return `${centerLng},${centerLat}`;
}

/**
 * 坐标系转换工具（预留）
 * 百度地图使用bd09ll坐标系，如果需要与其他坐标系转换可以使用此函数
 */
export const CoordinateConverter = {
	/**
	 * WGS84转百度坐标（预留接口）
	 * @param {number} lng - WGS84经度
	 * @param {number} lat - WGS84纬度
	 * @returns {Object} 百度坐标 {lng, lat}
	 */
	wgs84ToBd09ll(lng, lat) {
		// 实际项目中可以调用百度地图坐标转换API
		// 这里简单返回原坐标（需要根据实际需求实现）
		console.warn('⚠️ 坐标转换功能需要实现');
		return { lng, lat };
	},
	
	/**
	 * 国测局坐标转百度坐标（预留接口）
	 * @param {number} lng - GCJ02经度
	 * @param {number} lat - GCJ02纬度
	 * @returns {Object} 百度坐标 {lng, lat}
	 */
	gcj02ToBd09ll(lng, lat) {
		// 实际项目中可以调用百度地图坐标转换API
		// 这里简单返回原坐标（需要根据实际需求实现）
		console.warn('⚠️ 坐标转换功能需要实现');
		return { lng, lat };
	}
};

/**
 * 高级标记功能（预留扩展）
 */
export const AdvancedMarkers = {
	/**
	 * 生成自定义图标标记
	 * @param {Array} machines - 农机数据
	 * @param {string} iconUrl - 自定义图标URL
	 * @returns {Object} 标记数据
	 */
	generateCustomIconMarkers(machines = [], iconUrl = '') {
		const markers = [];
		const markerStyles = [];
		
		machines.forEach(machine => {
			if (machine.jw && machine.jw.includes(',')) {
				const [lng, lat] = machine.jw.split(',');
				if (lng && lat && !isNaN(lng) && !isNaN(lat)) {
					markers.push(`${lng},${lat}`);
					// 自定义图标格式：-1,图标URL,-1
					markerStyles.push(`-1,${iconUrl},-1`);
				}
			}
		});
		
		console.log('🎯 生成自定义图标标记:', { markers, markerStyles });
		return { markers, markerStyles };
	},
	
	/**
	 * 生成农机轨迹路径（预留）
	 * @param {Array} trackPoints - 轨迹点数组 [{lng, lat, timestamp}, ...]
	 * @returns {string} 百度地图路径参数
	 */
	generateMachineTrack(trackPoints = []) {
		if (trackPoints.length < 2) {
			return '';
		}
		
		const pathCoords = trackPoints.map(point => `${point.lng},${point.lat}`);
		const pathString = pathCoords.join(';');
		
		console.log('🛣️ 生成农机轨迹路径:', pathString);
		return pathString;
	},
	
	/**
	 * 生成带轨迹的地图URL（预留）
	 * @param {Object} options - 地图选项
	 * @param {Array} options.machines - 农机列表
	 * @param {Array} options.trackPoints - 轨迹点
	 * @param {string} options.pathColor - 轨迹颜色
	 * @returns {string} 地图URL
	 */
	generateTrackMapUrl(options = {}) {
		const {
			machines = [],
			trackPoints = [],
			pathColor = '0xFF0000', // 红色轨迹
			pathWeight = 5,
			pathOpacity = 0.8
		} = options;
		
		// 生成农机标记
		const { markers, markerStyles } = generateMachineMarkers(machines);
		
		// 生成轨迹路径
		const pathString = this.generateMachineTrack(trackPoints);
		
		// 构建地图URL
		let url = generateBaiduStaticMapUrl({
			markers,
			markerStyles
		});
		
		// 添加路径参数
		if (pathString) {
			url += `&paths=${pathString}`;
			url += `&pathStyles=${pathColor},${pathWeight},${pathOpacity}`;
		}
		
		console.log('🗺️ 生成带轨迹的地图URL:', url);
		return url;
	}
};

/**
 * 标签功能（预留扩展）
 */
export const LabelFeatures = {
	/**
	 * 生成农机信息标签
	 * @param {Array} machines - 农机数据
	 * @returns {Object} 标签数据
	 */
	generateMachineLabels(machines = []) {
		const labels = [];
		const labelStyles = [];
		
		machines.forEach(machine => {
			if (machine.jw && machine.jw.includes(',')) {
				const [lng, lat] = machine.jw.split(',');
				if (lng && lat && !isNaN(lng) && !isNaN(lat)) {
					labels.push(`${lng},${lat}`);
					
					// 标签内容：农机名称 + 状态
					const content = `${machine.fname || '农机'}\\n${this.getStatusText(machine.status)}`;
					const style = `${content},0,12,0xFFFFFF,0x000000,1`; // 白字黑底带边框
					labelStyles.push(style);
				}
			}
		});
		
		console.log('🏷️ 生成农机标签:', { labels, labelStyles });
		return { labels, labelStyles };
	},
	
	/**
	 * 获取状态文本
	 * @param {string} status - 状态
	 * @returns {string} 状态文本
	 */
	getStatusText(status) {
		const statusMap = {
			working: '作业中',
			idle: '空闲',
			maintenance: '维护中'
		};
		return statusMap[status] || '未知';
	},
	
	/**
	 * 生成带标签的地图URL
	 * @param {Array} machines - 农机数据
	 * @param {Object} mapOptions - 地图选项
	 * @returns {string} 地图URL
	 */
	generateLabelMapUrl(machines = [], mapOptions = {}) {
		const { markers, markerStyles } = generateMachineMarkers(machines);
		const { labels, labelStyles } = this.generateMachineLabels(machines);
		
		let url = generateBaiduStaticMapUrl({
			...mapOptions,
			markers,
			markerStyles
		});
		
		// 添加标签参数
		if (labels.length > 0) {
			url += `&labels=${labels.join('|')}`;
			url += `&labelStyles=${labelStyles.join('|')}`;
		}
		
		console.log('🗺️ 生成带标签的地图URL:', url);
		return url;
	}
};

/**
 * 地图工具函数导出
 */
export default {
	getBaiduLocationByIP,
	getDefaultLocation,
	generateBaiduStaticMapUrl,
	generateMachineMarkers,
	generateMachinesMapUrl,
	calculateMapCenter,
	CoordinateConverter,
	AdvancedMarkers,
	LabelFeatures
}; 